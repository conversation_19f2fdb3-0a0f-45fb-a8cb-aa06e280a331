[0m[[0m[0mdebug[0m] [0m[0mCopy resource mappings: [0m
[0m[[0m[0mdebug[0m] [0m[0m	(C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\src\main\resources\reference.conf,C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\target\scala-2.13\classes\reference.conf)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\src\main\resources\fonts\RobotoMono-Bold.ttf,C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\target\scala-2.13\classes\fonts\RobotoMono-Bold.ttf)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\src\main\resources\fonts\RobotoMono-Italic.ttf,C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\target\scala-2.13\classes\fonts\RobotoMono-Italic.ttf)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\src\main\resources\fonts\RobotoMono-Regular.ttf,C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\target\scala-2.13\classes\fonts\RobotoMono-Regular.ttf)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\src\main\resources\palettes\armytage_palette.txt,C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\target\scala-2.13\classes\palettes\armytage_palette.txt)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\src\main\resources\palettes\blues_palette.txt,C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\target\scala-2.13\classes\palettes\blues_palette.txt)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\src\main\resources\palettes\bw_palette.txt,C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\target\scala-2.13\classes\palettes\bw_palette.txt)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\src\main\resources\palettes\dark24_palette.txt,C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\target\scala-2.13\classes\palettes\dark24_palette.txt)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\src\main\resources\palettes\epic_palette.txt,C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\target\scala-2.13\classes\palettes\epic_palette.txt)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\src\main\resources\palettes\greens_palette.txt,C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\target\scala-2.13\classes\palettes\greens_palette.txt)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\src\main\resources\palettes\highcharts_palette.txt,C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\target\scala-2.13\classes\palettes\highcharts_palette.txt)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\src\main\resources\palettes\light24_palette.txt,C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\target\scala-2.13\classes\palettes\light24_palette.txt)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\src\main\resources\palettes\lightgray_palette.txt,C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\target\scala-2.13\classes\palettes\lightgray_palette.txt)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\src\main\resources\palettes\oranges_palette.txt,C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\target\scala-2.13\classes\palettes\oranges_palette.txt)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\src\main\resources\palettes\purples_palette.txt,C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\target\scala-2.13\classes\palettes\purples_palette.txt)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\src\main\resources\palettes\reds_palette.txt,C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\target\scala-2.13\classes\palettes\reds_palette.txt)[0m
