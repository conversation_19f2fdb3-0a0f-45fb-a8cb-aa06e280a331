'use client'
import { useState, useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import Interactive<PERSON>ogo from './InteractiveLogo'
import KineticText from './KineticText'

export default function HeroSection() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [particles, setParticles] = useState<JSX.Element[] | null>(null)
  const heroRef = useRef<HTMLDivElement>(null)
  const ctaRef = useRef<HTMLButtonElement>(null)

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const x = (e.clientX / window.innerWidth) * 2 - 1
      const y = -(e.clientY / window.innerHeight) * 2 + 1
      setMousePosition({ x, y })
    }

    window.addEventListener('mousemove', handleMouseMove)
    return () => window.removeEventListener('mousemove', handleMouseMove)
  }, [])

  useEffect(() => {
    const generatedParticles = [...Array(20)].map((_, i) => (
      <div
        key={i}
        className="absolute w-1 h-1 bg-white rounded-full animate-ping"
        style={{
          left: `${Math.random() * 100}%`,
          top: `${Math.random() * 100}%`,
          animationDelay: `${Math.random() * 2}s`,
        }}
      />
    ))
    setParticles(generatedParticles)
  }, [])

  const handleCTAClick = () => {
    if (!ctaRef.current) return

    gsap.timeline()
      .to(ctaRef.current, { scale: 0.95, duration: 0.1 })
      .to(ctaRef.current, { scale: 1.05, duration: 0.2, ease: 'back.out(1.7)' })
      .to(ctaRef.current, { scale: 1, duration: 0.1 })

    const flash = document.createElement('div')
    flash.className = 'fixed inset-0 bg-white pointer-events-none z-50'
    flash.style.opacity = '0'
    document.body.appendChild(flash)

    gsap.timeline()
      .to(flash, { opacity: 0.3, duration: 0.1 })
      .to(flash, { opacity: 0, duration: 0.3 })
      .call(() => document.body.removeChild(flash))
  }

  return (
    <section
      ref={heroRef}
      className="min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-700 relative overflow-hidden flex items-center justify-center"
    >
      {/* Animated background grid */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-[linear-gradient(rgba(0,245,255,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(0,245,255,0.1)_1px,transparent_1px)] bg-[size:50px_50px] animate-pulse" />
      </div>

      {/* Main content */}
      <div className="container mx-auto px-6 text-center relative z-10">
        {/* Interactive 3D Logo */}
        <InteractiveLogo mousePosition={mousePosition} />

        {/* Main headline with kinetic typography */}
        <div className="mt-8 mb-6">
          <KineticText
            text="FUTURE-PROOF"
            className="text-6xl md:text-8xl mb-4"
            delay={0.5}
          />
          <KineticText
            text="WEB DEVELOPMENT"
            className="text-4xl md:text-6xl mb-8"
            delay={1.2}
          />
        </div>

        {/* Subtitle */}
        <p className="text-xl md:text-2xl text-gray-300 mb-12 max-w-3xl mx-auto font-future">
          Crafting digital experiences that transcend time and space.
          <br />
          <span className="text-neon-blue">Welcome to the future of web.</span>
        </p>

        {/* CTA Button */}
        <button
          ref={ctaRef}
          onClick={handleCTAClick}
          className="group relative px-12 py-4 bg-gradient-to-r from-neon-blue to-neon-purple rounded-full font-cyber font-bold text-lg text-white overflow-hidden transition-all duration-300 hover:shadow-[0_0_40px_rgba(0,245,255,0.6)] animate-glow-pulse"
        >
          <span className="relative z-10">ENTER THE MATRIX</span>

          {/* Button background animation */}
          <div className="absolute inset-0 bg-gradient-to-r from-neon-purple to-neon-pink opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

          {/* Particle effect on hover */}
          <div className="absolute inset-0 opacity-0 group-hover:opacity-100 pointer-events-none">
            {particles}
          </div>
        </button>

        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-neon-blue rounded-full flex justify-center">
            <div className="w-1 h-3 bg-neon-blue rounded-full mt-2 animate-pulse" />
          </div>
        </div>
      </div>

      {/* Floating geometric shapes */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(5)].map((_, i) => (
          <div
            key={i}
            className="absolute w-20 h-20 border border-neon-purple opacity-20 animate-float"
            style={{
              left: `${20 + i * 15}%`,
              top: `${20 + i * 10}%`,
              animationDelay: `${i * 0.5}s`,
              clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)',
            }}
          />
        ))}
      </div>
    </section>
  )
}
