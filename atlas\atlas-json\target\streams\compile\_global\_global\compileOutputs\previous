["sbt.Task[scala.collection.Seq[java.nio.file.Path]]", ["C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-json\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\json\\Json.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-json\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\json\\JsonSupportSerializerModifier.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-json\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\json\\JsonParserHelper.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-json\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\json\\Json$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-json\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\json\\JsonParserHelper$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-json\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\json\\JsonSupport.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-json\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\json\\Json$Decoder.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-json\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\json\\JsonSupportSerializer.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-json\\target\\scala-2.13\\zinc\\inc_compile_2.13.zip"]]