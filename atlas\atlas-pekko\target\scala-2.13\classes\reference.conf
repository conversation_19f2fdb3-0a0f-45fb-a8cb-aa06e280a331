
atlas.pekko {

  # Regex that determines how the path tag value to use when mapping data about an
  # actor into a meter. There a should be a single capture group which will be the
  # path to report.
  path-pattern = "^pekko://(?:[^/]+)/(?:system|user)/([^$\\d/]+).*?$"

  # List of additional actor classes to load
  actors = ${?atlas.pekko.actors} [
    {
      name = "deadLetterStats"
      class = "com.netflix.atlas.pekko.DeadLetterStatsActor"
    }
  ]

  # List of classes to load as API endpoints
  api-endpoints = ${?atlas.pekko.api-endpoints} [
    "com.netflix.atlas.pekko.HealthcheckApi",
    "com.netflix.atlas.pekko.ConfigApi",
    "com.netflix.atlas.pekko.StaticPages"
  ]

  # Hosts that are allowed to make cross origin requests
  # 1. '*' match any host
  # 2. Starts with '.', indicates a sub-domain, for example `.netflix.com`. The origin must
  #    end with the exact string.
  # 3. Otherwise the hostname for the origin must match an entry exactly.
  #
  # An empty list means that CORS headers will not be added.
  cors-host-patterns = ["*"]

  # Headers added to all responses for diagnostic and logging purposes.
  diagnostic-headers = [
    #{
    #  name = "Netflix-ASG"
    #  value = ${NETFLIX_AUTO_SCALE_GROUP}
    #}
  ]

  # Options for the behavior of the default request handler
  request-handler {
    # Should the CORS handler be added? In some cases when proxying requests
    # it can be desirable to disable to avoid having multiple places trying
    # to process CORS headers.
    cors = true

    # Should it automatically handle compression of responses and decompressing
    # requests?
    compression = true

    # Should access logging be enabled?
    access-log = true

    # Probability for adding connection close header to response. A value of 0.0
    # means it will never be added, 1.0 means it will always be added.
    close-probability = 0.0
  }

  # Settings for the StaticPages API
  static {
    # Default page to redirect to when hitting /
    default-page = "/ui"

    # For single-page javascript apps, a list of prefixes and the page they map to. Any path with
    # that prefix will return the same page.
    single-page-prefixes = [
      {
        # Path prefix to use. Should be single-level, just the name, with no '/'.
        prefix = "ui"

        # Resource to fetch when the prefix is requested. This is the resource in the classpath
        # not path on the server.
        resource = "www/index.html"
      }
    ]
  }

  # Name of the actor system
  name = "atlas"

  # Ports to use for the web server
  ports = [
    {
      port = 7101
      secure = false
    },
    #{
    #  port = 7102
    #  secure = true
    #  context-factory = "com.netflix.atlas.pekko.ConfigConnectionContextFactory"
    #  ssl-config {
    #    // See https://lightbend.github.io/ssl-config/KeyStores.html
    #  }
    #}
  ]

  # How long to wait before giving up on bind
  bind-timeout = 5 seconds

  blocking-queue {
    # If the queue is empty when pulled, then it will keep checking until some new
    # data is available. This mechanism is simpler than juggling async callbacks and
    # ensures the overhead is limited to the queue. There is some risk if there is
    # bursty data that it could increase the rate of drops due to the queue being full
    # while waiting for the delay.
    frequency = 100ms
  }

  # Settings for spectator IPC logging
  ipc-logger {
    # Should inflight metrics be enabled? They are confusing and generally not useful.
    inflight-metrics-enabled = false

    # Maximum number of values permitted for tag keys on IPC metrics
    cardinality-limit {
      default = 25
      id = 100
    }

    # Size of queue for reusing logger entries
    entry-queue-size = 1000
  }
}

pekko {
  # Sometimes see timeouts related to logging, maybe related to something about M1 Macs
  logger-startup-timeout = 30s
  loggers = ["org.apache.pekko.event.slf4j.Slf4jLogger"]
  logging-filter = "org.apache.pekko.event.slf4j.Slf4jLoggingFilter"
  loglevel = "INFO"
  actor {
    debug {
      receive = on
      autoreceive = on
      lifecycle = on
    }

    default-dispatcher {
      fork-join-executor {
        parallelism-factor = 8.0
      }
    }

    deployment {
      /request-handler {
        router = round-robin-pool
        nr-of-instances = 8
      }
    }
  }

  io {
    tcp {
      max-channels = 256000
      trace-logging = off
    }
  }
}

pekko.http {
  client {
  }

  host-connection-pool {
    max-connections = 10
    max-retries = 0
    idle-timeout = 30 s
  }

  server {
    server-header = atlas/1.6

    # uncomment the next line for making this an HTTPS example
    # ssl-encryption = on
    idle-timeout = 30 s
    request-timeout = 10 s
    pipelining-limit = 20

    # Add the remote address into the request, useful for trying to figure out where traffic
    # is coming from.
    remote-address-header = on

    verbose-error-messages = on

  }

  parsing {
    uri-parsing-mode = relaxed
    max-uri-length = 4k
    max-content-length = 8m
  }
}
