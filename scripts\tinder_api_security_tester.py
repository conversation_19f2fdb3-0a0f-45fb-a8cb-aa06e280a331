#!/usr/bin/env python3
"""
Tinder API Security Tester
Focuses on real API endpoints, rate limiting, business logic, and mobile app APIs
"""

import requests
import json
import time
import sys
import random
import string
from urllib.parse import urljoin

class TinderAPISecurityTester:
    def __init__(self, base_urls):
        self.base_urls = base_urls if isinstance(base_urls, list) else [base_urls]
        self.session = requests.Session()
        self.vulnerabilities = []
        
        # Headers with bug bounty research identifier
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'X-HackerOne-Research': 'datafae',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Content-Type': 'application/json',
            'Origin': 'https://tinder.com',
            'Referer': 'https://tinder.com/'
        }
        
        self.session.headers.update(self.headers)
        
        # Real API endpoints discovered from reconnaissance
        self.api_endpoints = [
            # Authentication endpoints (discovered earlier)
            '/api/auth/login',
            '/api/auth/facebook',
            '/api/auth/google',
            '/api/auth/phone',
            '/api/auth/sms',
            '/oauth/token',
            
            # Common mobile app API patterns
            '/api/v1/auth',
            '/api/v2/auth', 
            '/api/v1/user',
            '/api/v2/user',
            '/api/v1/matches',
            '/api/v2/matches',
            '/api/v1/messages',
            '/api/v2/messages',
            
            # Discovery and recommendation APIs
            '/api/recs',
            '/api/recommendations',
            '/api/discover',
            '/api/nearby',
            '/api/feed',
            
            # User interaction APIs
            '/api/like',
            '/api/pass',
            '/api/superlike',
            '/api/boost',
            '/api/swipe',
            
            # Messaging APIs
            '/api/matches/{match_id}/messages',
            '/api/conversations',
            '/api/chat',
            
            # Profile and media APIs
            '/api/profile',
            '/api/photos',
            '/api/upload',
            '/api/media',
            
            # Premium features
            '/api/subscription',
            '/api/purchase',
            '/api/billing',
            '/api/payment',
            
            # Location and geo APIs
            '/api/location',
            '/api/geo',
            '/api/ping',
            
            # Admin and internal APIs
            '/api/admin',
            '/api/internal',
            '/api/debug',
            '/api/health',
            '/api/status',
            '/api/config',
            '/api/settings'
        ]
        
        # Mobile app specific headers
        self.mobile_headers = {
            'User-Agent': 'Tinder/12.14.0 (iPhone; iOS 15.0; Scale/3.00)',
            'X-Auth-Token': 'test_token_' + ''.join(random.choices(string.ascii_letters + string.digits, k=32)),
            'Tinder-Version': '12.14.0',
            'Platform': 'ios',
            'App-Version': '12.14.0',
            'X-HackerOne-Research': 'datafae'
        }
    
    def print_status(self, message, level="INFO"):
        colors = {"INFO": "\033[94m", "SUCCESS": "\033[92m", "WARNING": "\033[93m", "ERROR": "\033[91m", "RESET": "\033[0m"}
        print(f"{colors.get(level, '')}{message}{colors['RESET']}")
    
    def test_endpoint_discovery(self, base_url):
        """Discover real API endpoints"""
        self.print_status(f"[*] Discovering API endpoints on {base_url}", "INFO")
        discovered_endpoints = []
        
        for endpoint in self.api_endpoints:
            url = urljoin(base_url, endpoint)
            
            try:
                response = self.session.get(url, timeout=10)
                
                # Look for real API responses (not HTML fallbacks)
                if response.headers.get('Content-Type', '').startswith('application/json'):
                    discovered_endpoints.append((endpoint, response.status_code))
                    self.print_status(f"[+] Found JSON API: {endpoint} (Status: {response.status_code})", "SUCCESS")
                elif response.status_code in [401, 403, 405] and 'html' not in response.headers.get('Content-Type', '').lower():
                    discovered_endpoints.append((endpoint, response.status_code))
                    self.print_status(f"[+] Found protected API: {endpoint} (Status: {response.status_code})", "SUCCESS")
                elif response.status_code == 404 and len(response.content) < 1000:
                    # Real 404, not HTML fallback
                    pass
                elif 'tinder | dating' not in response.text.lower():
                    # Not the standard homepage fallback
                    discovered_endpoints.append((endpoint, response.status_code))
                    self.print_status(f"[+] Found potential API: {endpoint} (Status: {response.status_code})", "WARNING")
                    
            except requests.exceptions.RequestException:
                pass
        
        return discovered_endpoints
    
    def test_rate_limiting(self, base_url, endpoint):
        """Test for rate limiting vulnerabilities"""
        url = urljoin(base_url, endpoint)
        
        self.print_status(f"[*] Testing rate limiting on {endpoint}", "INFO")
        
        # Send rapid requests to test rate limiting
        request_count = 0
        start_time = time.time()
        
        for i in range(20):  # Send 20 rapid requests
            try:
                response = self.session.get(url, timeout=5)
                request_count += 1
                
                # Check for rate limiting responses
                if response.status_code == 429:
                    self.print_status(f"[+] Rate limiting detected after {request_count} requests", "SUCCESS")
                    return True
                elif 'rate limit' in response.text.lower() or 'too many requests' in response.text.lower():
                    self.print_status(f"[+] Rate limiting detected in response body", "SUCCESS")
                    return True
                    
                time.sleep(0.1)  # Small delay between requests
                
            except requests.exceptions.RequestException:
                continue
        
        elapsed_time = time.time() - start_time
        
        if request_count >= 15:  # If most requests succeeded
            vulnerability = {
                'type': 'Missing Rate Limiting',
                'url': url,
                'description': f'No rate limiting detected after {request_count} requests in {elapsed_time:.2f} seconds',
                'severity': 'Medium',
                'requests_sent': request_count,
                'time_elapsed': elapsed_time
            }
            
            self.vulnerabilities.append(vulnerability)
            self.print_status(f"[!] No rate limiting found on {endpoint}", "WARNING")
            return False
        
        return True
    
    def test_authentication_bypass(self, base_url, endpoint):
        """Test for authentication bypass in API endpoints"""
        url = urljoin(base_url, endpoint)
        
        # Test with mobile app headers (sometimes bypasses web auth)
        mobile_session = requests.Session()
        mobile_session.headers.update(self.mobile_headers)
        
        try:
            # Test with web session
            web_response = self.session.get(url, timeout=10)
            
            # Test with mobile headers
            mobile_response = mobile_session.get(url, timeout=10)
            
            # Compare responses
            if (web_response.status_code in [401, 403] and 
                mobile_response.status_code == 200 and 
                mobile_response.headers.get('Content-Type', '').startswith('application/json')):
                
                vulnerability = {
                    'type': 'Authentication Bypass via Mobile Headers',
                    'url': url,
                    'description': 'Mobile app headers bypass web authentication',
                    'severity': 'High',
                    'web_status': web_response.status_code,
                    'mobile_status': mobile_response.status_code
                }
                
                self.vulnerabilities.append(vulnerability)
                self.print_status(f"[!] Auth bypass found: {endpoint}", "ERROR")
                return True
                
        except requests.exceptions.RequestException:
            pass
        
        return False
    
    def test_business_logic_flaws(self, base_url):
        """Test for business logic flaws in dating app functionality"""
        self.print_status("[*] Testing business logic flaws", "INFO")
        
        # Test premium feature bypass
        premium_endpoints = [
            '/api/superlike',
            '/api/boost',
            '/api/subscription',
            '/api/premium'
        ]
        
        for endpoint in premium_endpoints:
            url = urljoin(base_url, endpoint)
            
            # Test POST requests with fake data
            test_data = {
                'user_id': '12345',
                'action': 'activate',
                'free_trial': True,
                'bypass': True
            }
            
            try:
                response = self.session.post(url, json=test_data, timeout=10)
                
                if response.status_code == 200:
                    try:
                        json_resp = response.json()
                        
                        # Look for success indicators
                        if any(word in str(json_resp).lower() for word in ['success', 'activated', 'granted']):
                            vulnerability = {
                                'type': 'Business Logic Flaw - Premium Feature Bypass',
                                'url': url,
                                'description': 'Premium features may be accessible without payment',
                                'severity': 'High',
                                'payload': test_data,
                                'response': json_resp
                            }
                            
                            self.vulnerabilities.append(vulnerability)
                            self.print_status(f"[!] Premium bypass found: {endpoint}", "ERROR")
                            
                    except json.JSONDecodeError:
                        pass
                        
            except requests.exceptions.RequestException:
                continue
    
    def test_information_disclosure(self, base_url):
        """Test for information disclosure in API responses"""
        self.print_status("[*] Testing for information disclosure", "INFO")
        
        info_endpoints = [
            '/api/config',
            '/api/settings',
            '/api/debug',
            '/api/health',
            '/api/status',
            '/api/version',
            '/api/info'
        ]
        
        for endpoint in info_endpoints:
            url = urljoin(base_url, endpoint)
            
            try:
                response = self.session.get(url, timeout=10)
                
                if (response.status_code == 200 and 
                    response.headers.get('Content-Type', '').startswith('application/json')):
                    
                    try:
                        json_resp = response.json()
                        
                        # Look for sensitive information
                        sensitive_keys = ['api_key', 'secret', 'password', 'token', 'database', 'internal', 'debug']
                        
                        found_sensitive = []
                        for key in sensitive_keys:
                            if key in str(json_resp).lower():
                                found_sensitive.append(key)
                        
                        if found_sensitive:
                            vulnerability = {
                                'type': 'Information Disclosure',
                                'url': url,
                                'description': f'Sensitive information exposed: {", ".join(found_sensitive)}',
                                'severity': 'Medium',
                                'sensitive_data': found_sensitive,
                                'response_preview': str(json_resp)[:300]
                            }
                            
                            self.vulnerabilities.append(vulnerability)
                            self.print_status(f"[!] Info disclosure: {endpoint}", "WARNING")
                            
                    except json.JSONDecodeError:
                        pass
                        
            except requests.exceptions.RequestException:
                continue
    
    def run_api_security_tests(self):
        """Run comprehensive API security tests"""
        self.print_status("[*] Starting Tinder API security tests...", "INFO")
        
        for base_url in self.base_urls:
            self.print_status(f"[*] Testing {base_url}", "INFO")
            
            # 1. Discover real API endpoints
            discovered_endpoints = self.test_endpoint_discovery(base_url)
            
            # 2. Test discovered endpoints
            for endpoint, status_code in discovered_endpoints[:5]:  # Test first 5 to avoid overwhelming
                # Test rate limiting
                self.test_rate_limiting(base_url, endpoint)
                
                # Test authentication bypass
                self.test_authentication_bypass(base_url, endpoint)
                
                time.sleep(0.5)  # Brief pause between tests
            
            # 3. Test business logic flaws
            self.test_business_logic_flaws(base_url)
            
            # 4. Test information disclosure
            self.test_information_disclosure(base_url)
        
        self.generate_report()
    
    def generate_report(self):
        """Generate API security vulnerability report"""
        if not self.vulnerabilities:
            self.print_status("[*] No API security vulnerabilities found.", "INFO")
            return
        
        self.print_status(f"\n[!] Found {len(self.vulnerabilities)} API security vulnerabilities:", "ERROR")
        
        report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'target_urls': self.base_urls,
            'vulnerabilities_found': len(self.vulnerabilities),
            'vulnerabilities': self.vulnerabilities
        }
        
        # Save detailed report
        with open('tinder_api_security_vulnerabilities.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        # Print summary
        severity_counts = {'Critical': 0, 'High': 0, 'Medium': 0, 'Low': 0}
        
        for vuln in self.vulnerabilities:
            severity_counts[vuln.get('severity', 'Medium')] += 1
        
        print(f"\n--- API Security Vulnerability Summary ---")
        for severity, count in severity_counts.items():
            if count > 0:
                print(f"{severity}: {count}")
        
        print(f"\n--- Detailed Findings ---")
        for i, vuln in enumerate(self.vulnerabilities, 1):
            print(f"\n{i}. {vuln['type']} ({vuln['severity']})")
            print(f"   URL: {vuln['url']}")
            print(f"   Description: {vuln['description']}")
        
        self.print_status(f"\n[+] Detailed report saved to tinder_api_security_vulnerabilities.json", "SUCCESS")

def main():
    if len(sys.argv) < 2:
        print("Usage: python tinder_api_security_tester.py <url1> [url2] [url3]...")
        print("Example: python tinder_api_security_tester.py https://www.tinder.com https://staging.tinder.com")
        sys.exit(1)
    
    urls = sys.argv[1:]
    tester = TinderAPISecurityTester(urls)
    tester.run_api_security_tests()

if __name__ == "__main__":
    main()
