["sbt.Task[scala.collection.Seq[java.nio.file.Path]]", ["C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\FetchRequestSource$EvalFlow$$anon$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\FetchRequestSource$EvalFlow.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\GraphRequestActor$$anonfun$innerReceive$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\LocalPublishActor$DatapointProcessor.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\LocalPublishActor$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\TagsApi$ListKeysRequest$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\TagsApi$Request.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\GraphApi$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\TagsApi$ListValuesRequest.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\PublishPayloads$$anonfun$com$netflix$atlas$webapi$PublishPayloads$$decodeTags$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\FetchRequestSource$DataChunk.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\TagsApi$ValueListResponse.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\PublishApi$PublishRequest.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\PublishPayloads$$anonfun$decodeBatch$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\PublishPayloads$$anonfun$com$netflix$atlas$webapi$PublishPayloads$$decode$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\PublishPayloads.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\TagsApi$ListTagsRequest$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\PublishConsumer$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\TagsApi.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\GraphRequestActor$$anonfun$receive$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\DatabaseSupplier.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\LocalDatabaseActor$$anonfun$receive$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\TagsApi$ListKeysRequest.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\PublishPayloads$$anonfun$decodeBatchDatapoints$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\TagsApi$TagListResponse.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\ExprApi$$anonfun$com$netflix$atlas$webapi$ExprApi$$stripKeys$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\FetchRequestSource$$anonfun$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\ExprApi$$anonfun$normalizeStat$1$$anonfun$3.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\ExprApi.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\ExprApi$$anonfun$normalizeStat$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\ExprApi$$anonfun$eval$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\ApiSettings$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\LocalPublishActor.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\PublishApi.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\GraphApi.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\PublishApi$PublishRequest$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\FetchRequestSource$DataChunk$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\FetchRequestSource$$anonfun$$nestedInanonfun$apply$1$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\ExprApi$$anonfun$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\ApiSettings.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\PublishApi$FailureMessage.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\LocalDatabaseActor.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\FetchRequestSource$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\GraphApi$DataResponse.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\TagsApi$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\ExprApi$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\ExprApi$$anonfun$4.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\TagsApi$EndOfResponse$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\PublishPayloads$$anonfun$com$netflix$atlas$webapi$PublishPayloads$$decodeDatapoint$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\TagsApi$ValueListResponse$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\GraphApi$DataRequest.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\PublishApi$FailureMessage$$anonfun$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\TagsApi$Request$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\PublishConsumer$ListPublishConsumer.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\ExprApi$$anonfun$stripFilter$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\TagsApi$KeyListResponse.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\TagsApi$ListTagsRequest.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\GraphRequestActor.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\TagsApi$KeyListResponse$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\PublishApi$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\TagsApi$TagListResponse$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\GraphApi$DataResponse$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\ExprApi$$anonfun$2.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\GraphApi$DataRequest$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\FetchRequestSource.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\PublishApi$FailureMessage$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\LocalDatabaseActor$$anonfun$com$netflix$atlas$webapi$LocalDatabaseActor$$innerReceive$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\PublishConsumer.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\TagsApi$ListValuesRequest$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\PublishPayloads$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\LocalPublishActor$$anonfun$receive$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\webapi\\ApiSettings$$anonfun$$lessinit$greater$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-webapi\\target\\scala-2.13\\zinc\\inc_compile_2.13.zip"]]