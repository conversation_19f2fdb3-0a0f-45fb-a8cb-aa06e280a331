{"cachedDescriptor": ".", "configurations": [{"configuration": {"name": "compile"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.google.code.findbugs", "name": "jsr305", "revision": "3.0.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsr305", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://findbugs.sourceforge.net/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-collection-compat_2.13", "revision": "2.13.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-collection-compat_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.typesafe.scala-logging", "name": "scala-logging_2.13", "revision": "3.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-logging_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/scala-logging", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0 License", "http://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.slf4j", "name": "slf4j-api", "revision": "2.0.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "slf4j-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.slf4j.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/license/mit"]], "callers": []}, {"module": {"organization": "com.netflix.spectator", "name": "spectator-api", "revision": "1.8.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spectator-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/spectator", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "config", "revision": "1.4.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "config", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-context", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-context", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-context/6.2.7/spring-context-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-context/6.2.7/spring-context-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-reflect", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-reflect", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-aop", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-aop", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-aop/6.2.7/spring-aop-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-aop/6.2.7/spring-aop-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-beans", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-beans", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-beans/6.2.7/spring-beans-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-beans/6.2.7/spring-beans-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-core", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-core", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-core/6.2.7/spring-core-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-core/6.2.7/spring-core-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-expression", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-expression", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-expression/6.2.7/spring-expression-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-expression/6.2.7/spring-expression-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "io.micrometer", "name": "micrometer-observation", "revision": "1.14.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "micrometer-observation", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/micrometer/micrometer-observation/1.14.7/micrometer-observation-1.14.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/io/micrometer/micrometer-observation/1.14.7/micrometer-observation-1.14.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/micrometer-metrics/micrometer", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-actor_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-actor_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-slf4j_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-slf4j_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-slf4j_2.13/1.1.3/pekko-slf4j_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-slf4j_2.13/1.1.3/pekko-slf4j_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-stream_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-stream_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "com.netflix.iep", "name": "iep-dynconfig", "revision": "5.1.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "iep-dynconfig", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/iep/iep-dynconfig/5.1.2/iep-dynconfig-5.1.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/iep/iep-dynconfig/5.1.2/iep-dynconfig-5.1.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/iep", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["APL2", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.netflix.iep", "name": "iep-service", "revision": "5.1.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "iep-service", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/iep/iep-service/5.1.2/iep-service-5.1.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/iep/iep-service/5.1.2/iep-service-5.1.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/iep", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["APL2", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-java8-compat_2.13", "revision": "1.0.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-java8-compat_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-java8-compat_2.13/1.0.2/scala-java8-compat_2.13-1.0.2.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-java8-compat_2.13/1.0.2/scala-java8-compat_2.13-1.0.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.netflix.spectator", "name": "spectator-ext-ipc", "revision": "1.8.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spectator-ext-ipc", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/spectator/spectator-ext-ipc/1.8.14/spectator-ext-ipc-1.8.14.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/spectator/spectator-ext-ipc/1.8.14/spectator-ext-ipc-1.8.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/spectator", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "com.github.ben-manes.caffeine", "name": "caffeine", "revision": "3.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "caffeine", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/3.2.0/caffeine-3.2.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/3.2.0/caffeine-3.2.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/ben-manes/caffeine", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.roaringbitmap", "name": "RoaringBitmap", "revision": "1.3.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "RoaringBitmap", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/roaringbitmap/RoaringBitmap/1.3.0/RoaringBitmap-1.3.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/roaringbitmap/RoaringBitmap/1.3.0/RoaringBitmap-1.3.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/RoaringBitmap/RoaringBitmap", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-jcl", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-jcl", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-jcl/6.2.7/spring-jcl-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-jcl/6.2.7/spring-jcl-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "io.micrometer", "name": "micrometer-commons", "revision": "1.14.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "micrometer-commons", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/micrometer/micrometer-commons/1.14.7/micrometer-commons-1.14.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/io/micrometer/micrometer-commons/1.14.7/micrometer-commons-1.14.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/micrometer-metrics/micrometer", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.core", "name": "jackson-core", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-core", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.19.0/jackson-core-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.19.0/jackson-core-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson-core", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.datatype", "name": "jackson-datatype-jdk8", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-datatype-jdk8", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.0/jackson-datatype-jdk8-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.0/jackson-datatype-jdk8-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.datatype", "name": "jackson-datatype-jsr310", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-datatype-jsr310", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.0/jackson-datatype-jsr310-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.0/jackson-datatype-jsr310-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.core", "name": "jackson-databind", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-databind", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.19.0/jackson-databind-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.19.0/jackson-databind-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.module", "name": "jackson-module-scala_2.13", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-module-scala_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/module/jackson-module-scala_2.13/2.19.0/jackson-module-scala_2.13-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/module/jackson-module-scala_2.13/2.19.0/jackson-module-scala_2.13-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson-module-scala", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.dataformat", "name": "jackson-dataformat-smile", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-dataformat-smile", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.19.0/jackson-dataformat-smile-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.19.0/jackson-dataformat-smile-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson-dataformats-binary", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-protobuf-v3_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-protobuf-v3_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.reactivestreams", "name": "reactive-streams", "revision": "1.0.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "reactive-streams", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.reactive-streams.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT-0", "https://spdx.org/licenses/MIT-0.html"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "ssl-config-core_2.13", "revision": "0.6.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "ssl-config-core_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/ssl-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "jakarta.annotation", "name": "jakarta.annotation-api", "revision": "3.0.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jakarta.annotation-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/jakarta/annotation/jakarta.annotation-api/3.0.0/jakarta.annotation-api-3.0.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/jakarta/annotation/jakarta.annotation-api/3.0.0/jakarta.annotation-api-3.0.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://projects.eclipse.org/projects/ee4j.ca", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["EPL 2.0", "https://www.eclipse.org/legal/epl-2.0"], ["GPL2 w/ CPE", "https://www.gnu.org/software/classpath/license.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http-core_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http-core_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.jspecify", "name": "jspecify", "revision": "1.0.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jspecify", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jspecify/jspecify/1.0.0/jspecify-1.0.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/jspecify/jspecify/1.0.0/jspecify-1.0.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://jspecify.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.google.errorprone", "name": "error_prone_annotations", "revision": "2.36.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "error_prone_annotations", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.36.0/error_prone_annotations-2.36.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.36.0/error_prone_annotations-2.36.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.core", "name": "jackson-annotations", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-annotations", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.19.0/jackson-annotations-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.19.0/jackson-annotations-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.thoughtworks.paranamer", "name": "paranamer", "revision": "2.8.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "paranamer", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/thoughtworks/paranamer/paranamer/2.8.3/paranamer-2.8.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/thoughtworks/paranamer/paranamer/2.8.3/paranamer-2.8.3.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD", "https://opensource.org/license/bsd-3-clause"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-parsing_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-parsing_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.parboiled", "name": "parboiled_2.13", "revision": "2.5.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "parboiled_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://parboiled.org", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-eval_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko-testkit_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-core_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-chart_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-json_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}], "details": []}, {"configuration": {"name": "compile-internal"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.google.code.findbugs", "name": "jsr305", "revision": "3.0.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsr305", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://findbugs.sourceforge.net/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-collection-compat_2.13", "revision": "2.13.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-collection-compat_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.typesafe.scala-logging", "name": "scala-logging_2.13", "revision": "3.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-logging_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/scala-logging", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0 License", "http://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.slf4j", "name": "slf4j-api", "revision": "2.0.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "slf4j-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.slf4j.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/license/mit"]], "callers": []}, {"module": {"organization": "com.netflix.spectator", "name": "spectator-api", "revision": "1.8.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spectator-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/spectator", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "config", "revision": "1.4.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "config", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-context", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-context", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-context/6.2.7/spring-context-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-context/6.2.7/spring-context-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-reflect", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-reflect", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-aop", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-aop", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-aop/6.2.7/spring-aop-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-aop/6.2.7/spring-aop-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-beans", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-beans", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-beans/6.2.7/spring-beans-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-beans/6.2.7/spring-beans-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-core", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-core", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-core/6.2.7/spring-core-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-core/6.2.7/spring-core-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-expression", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-expression", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-expression/6.2.7/spring-expression-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-expression/6.2.7/spring-expression-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "io.micrometer", "name": "micrometer-observation", "revision": "1.14.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "micrometer-observation", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/micrometer/micrometer-observation/1.14.7/micrometer-observation-1.14.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/io/micrometer/micrometer-observation/1.14.7/micrometer-observation-1.14.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/micrometer-metrics/micrometer", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-actor_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-actor_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-slf4j_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-slf4j_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-slf4j_2.13/1.1.3/pekko-slf4j_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-slf4j_2.13/1.1.3/pekko-slf4j_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-stream_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-stream_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "com.netflix.iep", "name": "iep-dynconfig", "revision": "5.1.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "iep-dynconfig", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/iep/iep-dynconfig/5.1.2/iep-dynconfig-5.1.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/iep/iep-dynconfig/5.1.2/iep-dynconfig-5.1.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/iep", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["APL2", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.netflix.iep", "name": "iep-service", "revision": "5.1.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "iep-service", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/iep/iep-service/5.1.2/iep-service-5.1.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/iep/iep-service/5.1.2/iep-service-5.1.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/iep", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["APL2", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-java8-compat_2.13", "revision": "1.0.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-java8-compat_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-java8-compat_2.13/1.0.2/scala-java8-compat_2.13-1.0.2.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-java8-compat_2.13/1.0.2/scala-java8-compat_2.13-1.0.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.netflix.spectator", "name": "spectator-ext-ipc", "revision": "1.8.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spectator-ext-ipc", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/spectator/spectator-ext-ipc/1.8.14/spectator-ext-ipc-1.8.14.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/spectator/spectator-ext-ipc/1.8.14/spectator-ext-ipc-1.8.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/spectator", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "com.github.ben-manes.caffeine", "name": "caffeine", "revision": "3.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "caffeine", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/3.2.0/caffeine-3.2.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/3.2.0/caffeine-3.2.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/ben-manes/caffeine", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.roaringbitmap", "name": "RoaringBitmap", "revision": "1.3.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "RoaringBitmap", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/roaringbitmap/RoaringBitmap/1.3.0/RoaringBitmap-1.3.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/roaringbitmap/RoaringBitmap/1.3.0/RoaringBitmap-1.3.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/RoaringBitmap/RoaringBitmap", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-jcl", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-jcl", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-jcl/6.2.7/spring-jcl-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-jcl/6.2.7/spring-jcl-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "io.micrometer", "name": "micrometer-commons", "revision": "1.14.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "micrometer-commons", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/micrometer/micrometer-commons/1.14.7/micrometer-commons-1.14.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/io/micrometer/micrometer-commons/1.14.7/micrometer-commons-1.14.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/micrometer-metrics/micrometer", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.core", "name": "jackson-core", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-core", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.19.0/jackson-core-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.19.0/jackson-core-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson-core", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.datatype", "name": "jackson-datatype-jdk8", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-datatype-jdk8", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.0/jackson-datatype-jdk8-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.0/jackson-datatype-jdk8-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.datatype", "name": "jackson-datatype-jsr310", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-datatype-jsr310", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.0/jackson-datatype-jsr310-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.0/jackson-datatype-jsr310-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.core", "name": "jackson-databind", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-databind", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.19.0/jackson-databind-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.19.0/jackson-databind-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.module", "name": "jackson-module-scala_2.13", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-module-scala_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/module/jackson-module-scala_2.13/2.19.0/jackson-module-scala_2.13-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/module/jackson-module-scala_2.13/2.19.0/jackson-module-scala_2.13-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson-module-scala", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.dataformat", "name": "jackson-dataformat-smile", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-dataformat-smile", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.19.0/jackson-dataformat-smile-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.19.0/jackson-dataformat-smile-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson-dataformats-binary", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-protobuf-v3_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-protobuf-v3_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.reactivestreams", "name": "reactive-streams", "revision": "1.0.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "reactive-streams", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.reactive-streams.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT-0", "https://spdx.org/licenses/MIT-0.html"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "ssl-config-core_2.13", "revision": "0.6.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "ssl-config-core_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/ssl-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "jakarta.annotation", "name": "jakarta.annotation-api", "revision": "3.0.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jakarta.annotation-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/jakarta/annotation/jakarta.annotation-api/3.0.0/jakarta.annotation-api-3.0.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/jakarta/annotation/jakarta.annotation-api/3.0.0/jakarta.annotation-api-3.0.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://projects.eclipse.org/projects/ee4j.ca", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["EPL 2.0", "https://www.eclipse.org/legal/epl-2.0"], ["GPL2 w/ CPE", "https://www.gnu.org/software/classpath/license.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http-core_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http-core_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.jspecify", "name": "jspecify", "revision": "1.0.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jspecify", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jspecify/jspecify/1.0.0/jspecify-1.0.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/jspecify/jspecify/1.0.0/jspecify-1.0.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://jspecify.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.google.errorprone", "name": "error_prone_annotations", "revision": "2.36.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "error_prone_annotations", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.36.0/error_prone_annotations-2.36.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.36.0/error_prone_annotations-2.36.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.core", "name": "jackson-annotations", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-annotations", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.19.0/jackson-annotations-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.19.0/jackson-annotations-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.thoughtworks.paranamer", "name": "paranamer", "revision": "2.8.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "paranamer", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/thoughtworks/paranamer/paranamer/2.8.3/paranamer-2.8.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/thoughtworks/paranamer/paranamer/2.8.3/paranamer-2.8.3.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD", "https://opensource.org/license/bsd-3-clause"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-parsing_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-parsing_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.parboiled", "name": "parboiled_2.13", "revision": "2.5.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "parboiled_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://parboiled.org", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-eval_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko-testkit_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-core_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-chart_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-json_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}], "details": []}, {"configuration": {"name": "docs"}, "modules": [{"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-eval_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko-testkit_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-core_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-chart_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-json_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}], "details": []}, {"configuration": {"name": "optional"}, "modules": [{"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-eval_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko-testkit_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-core_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-chart_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-json_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}], "details": []}, {"configuration": {"name": "plugin"}, "modules": [{"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-eval_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko-testkit_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-core_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-chart_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-json_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}], "details": []}, {"configuration": {"name": "pom"}, "modules": [{"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-eval_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko-testkit_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-core_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-chart_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-json_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}], "details": []}, {"configuration": {"name": "provided"}, "modules": [{"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-eval_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko-testkit_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-core_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-chart_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-json_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}], "details": []}, {"configuration": {"name": "runtime"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.google.code.findbugs", "name": "jsr305", "revision": "3.0.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsr305", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://findbugs.sourceforge.net/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-collection-compat_2.13", "revision": "2.13.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-collection-compat_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.typesafe.scala-logging", "name": "scala-logging_2.13", "revision": "3.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-logging_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/scala-logging", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0 License", "http://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.slf4j", "name": "slf4j-api", "revision": "2.0.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "slf4j-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.slf4j.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/license/mit"]], "callers": []}, {"module": {"organization": "com.netflix.spectator", "name": "spectator-api", "revision": "1.8.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spectator-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/spectator", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "config", "revision": "1.4.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "config", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-context", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-context", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-context/6.2.7/spring-context-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-context/6.2.7/spring-context-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-reflect", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-reflect", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-aop", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-aop", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-aop/6.2.7/spring-aop-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-aop/6.2.7/spring-aop-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-beans", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-beans", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-beans/6.2.7/spring-beans-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-beans/6.2.7/spring-beans-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-core", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-core", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-core/6.2.7/spring-core-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-core/6.2.7/spring-core-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-expression", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-expression", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-expression/6.2.7/spring-expression-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-expression/6.2.7/spring-expression-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "io.micrometer", "name": "micrometer-observation", "revision": "1.14.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "micrometer-observation", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/micrometer/micrometer-observation/1.14.7/micrometer-observation-1.14.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/io/micrometer/micrometer-observation/1.14.7/micrometer-observation-1.14.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/micrometer-metrics/micrometer", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-actor_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-actor_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-slf4j_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-slf4j_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-slf4j_2.13/1.1.3/pekko-slf4j_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-slf4j_2.13/1.1.3/pekko-slf4j_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-stream_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-stream_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "com.netflix.iep", "name": "iep-dynconfig", "revision": "5.1.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "iep-dynconfig", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/iep/iep-dynconfig/5.1.2/iep-dynconfig-5.1.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/iep/iep-dynconfig/5.1.2/iep-dynconfig-5.1.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/iep", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["APL2", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.netflix.iep", "name": "iep-service", "revision": "5.1.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "iep-service", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/iep/iep-service/5.1.2/iep-service-5.1.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/iep/iep-service/5.1.2/iep-service-5.1.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/iep", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["APL2", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-java8-compat_2.13", "revision": "1.0.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-java8-compat_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-java8-compat_2.13/1.0.2/scala-java8-compat_2.13-1.0.2.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-java8-compat_2.13/1.0.2/scala-java8-compat_2.13-1.0.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.netflix.spectator", "name": "spectator-ext-ipc", "revision": "1.8.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spectator-ext-ipc", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/spectator/spectator-ext-ipc/1.8.14/spectator-ext-ipc-1.8.14.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/spectator/spectator-ext-ipc/1.8.14/spectator-ext-ipc-1.8.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/spectator", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "com.github.ben-manes.caffeine", "name": "caffeine", "revision": "3.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "caffeine", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/3.2.0/caffeine-3.2.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/3.2.0/caffeine-3.2.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/ben-manes/caffeine", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.roaringbitmap", "name": "RoaringBitmap", "revision": "1.3.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "RoaringBitmap", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/roaringbitmap/RoaringBitmap/1.3.0/RoaringBitmap-1.3.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/roaringbitmap/RoaringBitmap/1.3.0/RoaringBitmap-1.3.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/RoaringBitmap/RoaringBitmap", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-jcl", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-jcl", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-jcl/6.2.7/spring-jcl-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-jcl/6.2.7/spring-jcl-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "io.micrometer", "name": "micrometer-commons", "revision": "1.14.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "micrometer-commons", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/micrometer/micrometer-commons/1.14.7/micrometer-commons-1.14.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/io/micrometer/micrometer-commons/1.14.7/micrometer-commons-1.14.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/micrometer-metrics/micrometer", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.core", "name": "jackson-core", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-core", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.19.0/jackson-core-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.19.0/jackson-core-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson-core", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.datatype", "name": "jackson-datatype-jdk8", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-datatype-jdk8", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.0/jackson-datatype-jdk8-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.0/jackson-datatype-jdk8-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.datatype", "name": "jackson-datatype-jsr310", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-datatype-jsr310", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.0/jackson-datatype-jsr310-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.0/jackson-datatype-jsr310-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.core", "name": "jackson-databind", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-databind", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.19.0/jackson-databind-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.19.0/jackson-databind-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.module", "name": "jackson-module-scala_2.13", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-module-scala_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/module/jackson-module-scala_2.13/2.19.0/jackson-module-scala_2.13-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/module/jackson-module-scala_2.13/2.19.0/jackson-module-scala_2.13-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson-module-scala", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.dataformat", "name": "jackson-dataformat-smile", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-dataformat-smile", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.19.0/jackson-dataformat-smile-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.19.0/jackson-dataformat-smile-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson-dataformats-binary", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-protobuf-v3_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-protobuf-v3_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.reactivestreams", "name": "reactive-streams", "revision": "1.0.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "reactive-streams", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.reactive-streams.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT-0", "https://spdx.org/licenses/MIT-0.html"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "ssl-config-core_2.13", "revision": "0.6.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "ssl-config-core_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/ssl-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "jakarta.annotation", "name": "jakarta.annotation-api", "revision": "3.0.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jakarta.annotation-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/jakarta/annotation/jakarta.annotation-api/3.0.0/jakarta.annotation-api-3.0.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/jakarta/annotation/jakarta.annotation-api/3.0.0/jakarta.annotation-api-3.0.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://projects.eclipse.org/projects/ee4j.ca", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["EPL 2.0", "https://www.eclipse.org/legal/epl-2.0"], ["GPL2 w/ CPE", "https://www.gnu.org/software/classpath/license.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http-core_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http-core_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.jspecify", "name": "jspecify", "revision": "1.0.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jspecify", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jspecify/jspecify/1.0.0/jspecify-1.0.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/jspecify/jspecify/1.0.0/jspecify-1.0.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://jspecify.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.google.errorprone", "name": "error_prone_annotations", "revision": "2.36.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "error_prone_annotations", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.36.0/error_prone_annotations-2.36.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.36.0/error_prone_annotations-2.36.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.core", "name": "jackson-annotations", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-annotations", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.19.0/jackson-annotations-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.19.0/jackson-annotations-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.thoughtworks.paranamer", "name": "paranamer", "revision": "2.8.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "paranamer", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/thoughtworks/paranamer/paranamer/2.8.3/paranamer-2.8.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/thoughtworks/paranamer/paranamer/2.8.3/paranamer-2.8.3.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD", "https://opensource.org/license/bsd-3-clause"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-parsing_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-parsing_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.parboiled", "name": "parboiled_2.13", "revision": "2.5.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "parboiled_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://parboiled.org", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-eval_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko-testkit_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-core_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-chart_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-json_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}], "details": []}, {"configuration": {"name": "runtime-internal"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.google.code.findbugs", "name": "jsr305", "revision": "3.0.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsr305", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://findbugs.sourceforge.net/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-collection-compat_2.13", "revision": "2.13.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-collection-compat_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.typesafe.scala-logging", "name": "scala-logging_2.13", "revision": "3.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-logging_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/scala-logging", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0 License", "http://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.slf4j", "name": "slf4j-api", "revision": "2.0.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "slf4j-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.slf4j.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/license/mit"]], "callers": []}, {"module": {"organization": "com.netflix.spectator", "name": "spectator-api", "revision": "1.8.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spectator-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/spectator", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "config", "revision": "1.4.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "config", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-context", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-context", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-context/6.2.7/spring-context-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-context/6.2.7/spring-context-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-reflect", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-reflect", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-aop", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-aop", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-aop/6.2.7/spring-aop-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-aop/6.2.7/spring-aop-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-beans", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-beans", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-beans/6.2.7/spring-beans-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-beans/6.2.7/spring-beans-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-core", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-core", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-core/6.2.7/spring-core-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-core/6.2.7/spring-core-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-expression", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-expression", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-expression/6.2.7/spring-expression-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-expression/6.2.7/spring-expression-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "io.micrometer", "name": "micrometer-observation", "revision": "1.14.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "micrometer-observation", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/micrometer/micrometer-observation/1.14.7/micrometer-observation-1.14.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/io/micrometer/micrometer-observation/1.14.7/micrometer-observation-1.14.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/micrometer-metrics/micrometer", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-actor_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-actor_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-slf4j_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-slf4j_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-slf4j_2.13/1.1.3/pekko-slf4j_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-slf4j_2.13/1.1.3/pekko-slf4j_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-stream_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-stream_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "com.netflix.iep", "name": "iep-dynconfig", "revision": "5.1.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "iep-dynconfig", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/iep/iep-dynconfig/5.1.2/iep-dynconfig-5.1.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/iep/iep-dynconfig/5.1.2/iep-dynconfig-5.1.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/iep", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["APL2", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.netflix.iep", "name": "iep-service", "revision": "5.1.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "iep-service", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/iep/iep-service/5.1.2/iep-service-5.1.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/iep/iep-service/5.1.2/iep-service-5.1.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/iep", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["APL2", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-java8-compat_2.13", "revision": "1.0.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-java8-compat_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-java8-compat_2.13/1.0.2/scala-java8-compat_2.13-1.0.2.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-java8-compat_2.13/1.0.2/scala-java8-compat_2.13-1.0.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.netflix.spectator", "name": "spectator-ext-ipc", "revision": "1.8.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spectator-ext-ipc", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/spectator/spectator-ext-ipc/1.8.14/spectator-ext-ipc-1.8.14.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/spectator/spectator-ext-ipc/1.8.14/spectator-ext-ipc-1.8.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/spectator", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "com.github.ben-manes.caffeine", "name": "caffeine", "revision": "3.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "caffeine", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/3.2.0/caffeine-3.2.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/3.2.0/caffeine-3.2.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/ben-manes/caffeine", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.roaringbitmap", "name": "RoaringBitmap", "revision": "1.3.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "RoaringBitmap", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/roaringbitmap/RoaringBitmap/1.3.0/RoaringBitmap-1.3.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/roaringbitmap/RoaringBitmap/1.3.0/RoaringBitmap-1.3.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/RoaringBitmap/RoaringBitmap", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-jcl", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-jcl", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-jcl/6.2.7/spring-jcl-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-jcl/6.2.7/spring-jcl-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "io.micrometer", "name": "micrometer-commons", "revision": "1.14.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "micrometer-commons", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/micrometer/micrometer-commons/1.14.7/micrometer-commons-1.14.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/io/micrometer/micrometer-commons/1.14.7/micrometer-commons-1.14.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/micrometer-metrics/micrometer", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.core", "name": "jackson-core", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-core", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.19.0/jackson-core-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.19.0/jackson-core-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson-core", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.datatype", "name": "jackson-datatype-jdk8", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-datatype-jdk8", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.0/jackson-datatype-jdk8-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.0/jackson-datatype-jdk8-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.datatype", "name": "jackson-datatype-jsr310", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-datatype-jsr310", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.0/jackson-datatype-jsr310-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.0/jackson-datatype-jsr310-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.core", "name": "jackson-databind", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-databind", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.19.0/jackson-databind-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.19.0/jackson-databind-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.module", "name": "jackson-module-scala_2.13", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-module-scala_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/module/jackson-module-scala_2.13/2.19.0/jackson-module-scala_2.13-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/module/jackson-module-scala_2.13/2.19.0/jackson-module-scala_2.13-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson-module-scala", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.dataformat", "name": "jackson-dataformat-smile", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-dataformat-smile", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.19.0/jackson-dataformat-smile-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.19.0/jackson-dataformat-smile-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson-dataformats-binary", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-protobuf-v3_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-protobuf-v3_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.reactivestreams", "name": "reactive-streams", "revision": "1.0.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "reactive-streams", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.reactive-streams.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT-0", "https://spdx.org/licenses/MIT-0.html"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "ssl-config-core_2.13", "revision": "0.6.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "ssl-config-core_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/ssl-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "jakarta.annotation", "name": "jakarta.annotation-api", "revision": "3.0.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jakarta.annotation-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/jakarta/annotation/jakarta.annotation-api/3.0.0/jakarta.annotation-api-3.0.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/jakarta/annotation/jakarta.annotation-api/3.0.0/jakarta.annotation-api-3.0.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://projects.eclipse.org/projects/ee4j.ca", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["EPL 2.0", "https://www.eclipse.org/legal/epl-2.0"], ["GPL2 w/ CPE", "https://www.gnu.org/software/classpath/license.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http-core_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http-core_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.jspecify", "name": "jspecify", "revision": "1.0.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jspecify", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jspecify/jspecify/1.0.0/jspecify-1.0.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/jspecify/jspecify/1.0.0/jspecify-1.0.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://jspecify.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.google.errorprone", "name": "error_prone_annotations", "revision": "2.36.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "error_prone_annotations", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.36.0/error_prone_annotations-2.36.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.36.0/error_prone_annotations-2.36.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.core", "name": "jackson-annotations", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-annotations", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.19.0/jackson-annotations-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.19.0/jackson-annotations-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.thoughtworks.paranamer", "name": "paranamer", "revision": "2.8.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "paranamer", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/thoughtworks/paranamer/paranamer/2.8.3/paranamer-2.8.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/thoughtworks/paranamer/paranamer/2.8.3/paranamer-2.8.3.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD", "https://opensource.org/license/bsd-3-clause"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-parsing_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-parsing_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.parboiled", "name": "parboiled_2.13", "revision": "2.5.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "parboiled_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://parboiled.org", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-eval_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko-testkit_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-core_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-chart_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-json_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}], "details": []}, {"configuration": {"name": "scala-doc-tool"}, "modules": [{"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-eval_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko-testkit_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-core_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-chart_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-json_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}], "details": []}, {"configuration": {"name": "scala-tool"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-compiler", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-compiler/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-compiler", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.13.16/scala-compiler-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-compiler/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.13.16/scala-compiler-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-compiler/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-compiler", "revision": "2.13.16", "configurations": "optional", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-compiler/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-compiler", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.13.16/scala-compiler-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-compiler/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.13.16/scala-compiler-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-compiler/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.16", "configurations": "optional", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-reflect", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-reflect", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "io.github.java-diff-utils", "name": "java-diff-utils", "revision": "4.15", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "java-diff-utils", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/github/java-diff-utils/java-diff-utils/4.15/java-diff-utils-4.15.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/io/github/java-diff-utils/java-diff-utils/4.15/java-diff-utils-4.15.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.jline", "name": "jline", "revision": "3.27.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline", "type": "jar", "extension": "jar", "classifier": "jdk8", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline/3.27.1/jline-3.27.1-jdk8.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/jline/jline/3.27.1/jline-3.27.1-jdk8.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The BSD License", "https://opensource.org/licenses/BSD-3-Clause"]], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-eval_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko-testkit_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-core_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-chart_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-json_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}], "details": []}, {"configuration": {"name": "sources"}, "modules": [{"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-eval_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko-testkit_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-core_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-chart_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-json_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}], "details": []}, {"configuration": {"name": "test"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.google.code.findbugs", "name": "jsr305", "revision": "3.0.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsr305", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://findbugs.sourceforge.net/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-collection-compat_2.13", "revision": "2.13.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-collection-compat_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.typesafe.scala-logging", "name": "scala-logging_2.13", "revision": "3.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-logging_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/scala-logging", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0 License", "http://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.slf4j", "name": "slf4j-api", "revision": "2.0.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "slf4j-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.slf4j.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/license/mit"]], "callers": []}, {"module": {"organization": "com.netflix.spectator", "name": "spectator-api", "revision": "1.8.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spectator-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/spectator", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "config", "revision": "1.4.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "config", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "munit_2.13", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "munit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/munit_2.13/1.1.1/munit_2.13-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/munit_2.13/1.1.1/munit_2.13-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-context", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-context", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-context/6.2.7/spring-context-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-context/6.2.7/spring-context-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-reflect", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-reflect", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "junit-interface", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "junit-interface", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/junit-interface/1.1.1/junit-interface-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/junit-interface/1.1.1/junit-interface-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "munit-diff_2.13", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "munit-diff_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/munit-diff_2.13/1.1.1/munit-diff_2.13-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/munit-diff_2.13/1.1.1/munit-diff_2.13-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "junit", "name": "junit", "revision": "4.13.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "junit", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/junit/junit/4.13.2/junit-4.13.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/junit/junit/4.13.2/junit-4.13.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://junit.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Eclipse Public License 1.0", "http://www.eclipse.org/legal/epl-v10.html"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-aop", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-aop", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-aop/6.2.7/spring-aop-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-aop/6.2.7/spring-aop-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-beans", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-beans", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-beans/6.2.7/spring-beans-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-beans/6.2.7/spring-beans-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-core", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-core", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-core/6.2.7/spring-core-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-core/6.2.7/spring-core-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-expression", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-expression", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-expression/6.2.7/spring-expression-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-expression/6.2.7/spring-expression-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "io.micrometer", "name": "micrometer-observation", "revision": "1.14.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "micrometer-observation", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/micrometer/micrometer-observation/1.14.7/micrometer-observation-1.14.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/io/micrometer/micrometer-observation/1.14.7/micrometer-observation-1.14.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/micrometer-metrics/micrometer", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-actor_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-actor_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-slf4j_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-slf4j_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-slf4j_2.13/1.1.3/pekko-slf4j_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-slf4j_2.13/1.1.3/pekko-slf4j_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-stream_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-stream_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "com.netflix.iep", "name": "iep-dynconfig", "revision": "5.1.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "iep-dynconfig", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/iep/iep-dynconfig/5.1.2/iep-dynconfig-5.1.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/iep/iep-dynconfig/5.1.2/iep-dynconfig-5.1.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/iep", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["APL2", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.netflix.iep", "name": "iep-service", "revision": "5.1.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "iep-service", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/iep/iep-service/5.1.2/iep-service-5.1.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/iep/iep-service/5.1.2/iep-service-5.1.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/iep", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["APL2", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-java8-compat_2.13", "revision": "1.0.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-java8-compat_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-java8-compat_2.13/1.0.2/scala-java8-compat_2.13-1.0.2.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-java8-compat_2.13/1.0.2/scala-java8-compat_2.13-1.0.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.netflix.spectator", "name": "spectator-ext-ipc", "revision": "1.8.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spectator-ext-ipc", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/spectator/spectator-ext-ipc/1.8.14/spectator-ext-ipc-1.8.14.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/spectator/spectator-ext-ipc/1.8.14/spectator-ext-ipc-1.8.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/spectator", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "com.github.ben-manes.caffeine", "name": "caffeine", "revision": "3.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "caffeine", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/3.2.0/caffeine-3.2.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/3.2.0/caffeine-3.2.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/ben-manes/caffeine", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.roaringbitmap", "name": "RoaringBitmap", "revision": "1.3.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "RoaringBitmap", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/roaringbitmap/RoaringBitmap/1.3.0/RoaringBitmap-1.3.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/roaringbitmap/RoaringBitmap/1.3.0/RoaringBitmap-1.3.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/RoaringBitmap/RoaringBitmap", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "test-interface", "revision": "1.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "test-interface", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-sbt.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD", "https://github.com/sbt/test-interface/blob/master/LICENSE"]], "callers": []}, {"module": {"organization": "org.hamcrest", "name": "hamcrest-core", "revision": "1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "hamcrest-core", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["New BSD License", "http://www.opensource.org/licenses/bsd-license.php"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-jcl", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-jcl", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-jcl/6.2.7/spring-jcl-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-jcl/6.2.7/spring-jcl-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "io.micrometer", "name": "micrometer-commons", "revision": "1.14.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "micrometer-commons", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/micrometer/micrometer-commons/1.14.7/micrometer-commons-1.14.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/io/micrometer/micrometer-commons/1.14.7/micrometer-commons-1.14.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/micrometer-metrics/micrometer", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.core", "name": "jackson-core", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-core", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.19.0/jackson-core-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.19.0/jackson-core-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson-core", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.datatype", "name": "jackson-datatype-jdk8", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-datatype-jdk8", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.0/jackson-datatype-jdk8-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.0/jackson-datatype-jdk8-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.datatype", "name": "jackson-datatype-jsr310", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-datatype-jsr310", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.0/jackson-datatype-jsr310-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.0/jackson-datatype-jsr310-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.core", "name": "jackson-databind", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-databind", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.19.0/jackson-databind-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.19.0/jackson-databind-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.module", "name": "jackson-module-scala_2.13", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-module-scala_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/module/jackson-module-scala_2.13/2.19.0/jackson-module-scala_2.13-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/module/jackson-module-scala_2.13/2.19.0/jackson-module-scala_2.13-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson-module-scala", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.dataformat", "name": "jackson-dataformat-smile", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-dataformat-smile", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.19.0/jackson-dataformat-smile-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.19.0/jackson-dataformat-smile-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson-dataformats-binary", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-protobuf-v3_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-protobuf-v3_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.reactivestreams", "name": "reactive-streams", "revision": "1.0.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "reactive-streams", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.reactive-streams.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT-0", "https://spdx.org/licenses/MIT-0.html"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "ssl-config-core_2.13", "revision": "0.6.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "ssl-config-core_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/ssl-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "jakarta.annotation", "name": "jakarta.annotation-api", "revision": "3.0.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jakarta.annotation-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/jakarta/annotation/jakarta.annotation-api/3.0.0/jakarta.annotation-api-3.0.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/jakarta/annotation/jakarta.annotation-api/3.0.0/jakarta.annotation-api-3.0.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://projects.eclipse.org/projects/ee4j.ca", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["EPL 2.0", "https://www.eclipse.org/legal/epl-2.0"], ["GPL2 w/ CPE", "https://www.gnu.org/software/classpath/license.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http-core_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http-core_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.jspecify", "name": "jspecify", "revision": "1.0.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jspecify", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jspecify/jspecify/1.0.0/jspecify-1.0.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/jspecify/jspecify/1.0.0/jspecify-1.0.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://jspecify.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.google.errorprone", "name": "error_prone_annotations", "revision": "2.36.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "error_prone_annotations", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.36.0/error_prone_annotations-2.36.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.36.0/error_prone_annotations-2.36.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.core", "name": "jackson-annotations", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-annotations", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.19.0/jackson-annotations-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.19.0/jackson-annotations-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.thoughtworks.paranamer", "name": "paranamer", "revision": "2.8.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "paranamer", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/thoughtworks/paranamer/paranamer/2.8.3/paranamer-2.8.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/thoughtworks/paranamer/paranamer/2.8.3/paranamer-2.8.3.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD", "https://opensource.org/license/bsd-3-clause"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-parsing_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-parsing_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.parboiled", "name": "parboiled_2.13", "revision": "2.5.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "parboiled_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://parboiled.org", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-eval_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko-testkit_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-core_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-chart_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-json_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}], "details": []}, {"configuration": {"name": "test-internal"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.google.code.findbugs", "name": "jsr305", "revision": "3.0.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsr305", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://findbugs.sourceforge.net/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-collection-compat_2.13", "revision": "2.13.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-collection-compat_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.typesafe.scala-logging", "name": "scala-logging_2.13", "revision": "3.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-logging_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/scala-logging", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0 License", "http://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.slf4j", "name": "slf4j-api", "revision": "2.0.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "slf4j-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.slf4j.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/license/mit"]], "callers": []}, {"module": {"organization": "com.netflix.spectator", "name": "spectator-api", "revision": "1.8.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spectator-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/spectator", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "config", "revision": "1.4.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "config", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "munit_2.13", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "munit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/munit_2.13/1.1.1/munit_2.13-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/munit_2.13/1.1.1/munit_2.13-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-context", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-context", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-context/6.2.7/spring-context-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-context/6.2.7/spring-context-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-reflect", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-reflect", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "junit-interface", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "junit-interface", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/junit-interface/1.1.1/junit-interface-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/junit-interface/1.1.1/junit-interface-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "munit-diff_2.13", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "munit-diff_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/munit-diff_2.13/1.1.1/munit-diff_2.13-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/munit-diff_2.13/1.1.1/munit-diff_2.13-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "junit", "name": "junit", "revision": "4.13.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "junit", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/junit/junit/4.13.2/junit-4.13.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/junit/junit/4.13.2/junit-4.13.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://junit.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Eclipse Public License 1.0", "http://www.eclipse.org/legal/epl-v10.html"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-aop", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-aop", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-aop/6.2.7/spring-aop-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-aop/6.2.7/spring-aop-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-beans", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-beans", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-beans/6.2.7/spring-beans-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-beans/6.2.7/spring-beans-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-core", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-core", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-core/6.2.7/spring-core-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-core/6.2.7/spring-core-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-expression", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-expression", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-expression/6.2.7/spring-expression-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-expression/6.2.7/spring-expression-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "io.micrometer", "name": "micrometer-observation", "revision": "1.14.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "micrometer-observation", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/micrometer/micrometer-observation/1.14.7/micrometer-observation-1.14.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/io/micrometer/micrometer-observation/1.14.7/micrometer-observation-1.14.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/micrometer-metrics/micrometer", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-actor_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-actor_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-slf4j_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-slf4j_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-slf4j_2.13/1.1.3/pekko-slf4j_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-slf4j_2.13/1.1.3/pekko-slf4j_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-stream_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-stream_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "com.netflix.iep", "name": "iep-dynconfig", "revision": "5.1.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "iep-dynconfig", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/iep/iep-dynconfig/5.1.2/iep-dynconfig-5.1.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/iep/iep-dynconfig/5.1.2/iep-dynconfig-5.1.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/iep", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["APL2", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.netflix.iep", "name": "iep-service", "revision": "5.1.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "iep-service", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/iep/iep-service/5.1.2/iep-service-5.1.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/iep/iep-service/5.1.2/iep-service-5.1.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/iep", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["APL2", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-java8-compat_2.13", "revision": "1.0.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-java8-compat_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-java8-compat_2.13/1.0.2/scala-java8-compat_2.13-1.0.2.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-java8-compat_2.13/1.0.2/scala-java8-compat_2.13-1.0.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.netflix.spectator", "name": "spectator-ext-ipc", "revision": "1.8.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spectator-ext-ipc", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/spectator/spectator-ext-ipc/1.8.14/spectator-ext-ipc-1.8.14.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/spectator/spectator-ext-ipc/1.8.14/spectator-ext-ipc-1.8.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/spectator", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "com.github.ben-manes.caffeine", "name": "caffeine", "revision": "3.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "caffeine", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/3.2.0/caffeine-3.2.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/3.2.0/caffeine-3.2.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/ben-manes/caffeine", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.roaringbitmap", "name": "RoaringBitmap", "revision": "1.3.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "RoaringBitmap", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/roaringbitmap/RoaringBitmap/1.3.0/RoaringBitmap-1.3.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/roaringbitmap/RoaringBitmap/1.3.0/RoaringBitmap-1.3.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/RoaringBitmap/RoaringBitmap", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "test-interface", "revision": "1.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "test-interface", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-sbt.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD", "https://github.com/sbt/test-interface/blob/master/LICENSE"]], "callers": []}, {"module": {"organization": "org.hamcrest", "name": "hamcrest-core", "revision": "1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "hamcrest-core", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["New BSD License", "http://www.opensource.org/licenses/bsd-license.php"]], "callers": []}, {"module": {"organization": "org.springframework", "name": "spring-jcl", "revision": "6.2.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spring-jcl", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/springframework/spring-jcl/6.2.7/spring-jcl-6.2.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/springframework/spring-jcl/6.2.7/spring-jcl-6.2.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/spring-projects/spring-framework", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "io.micrometer", "name": "micrometer-commons", "revision": "1.14.7", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "micrometer-commons", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/micrometer/micrometer-commons/1.14.7/micrometer-commons-1.14.7.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/io/micrometer/micrometer-commons/1.14.7/micrometer-commons-1.14.7.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/micrometer-metrics/micrometer", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.core", "name": "jackson-core", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-core", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.19.0/jackson-core-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.19.0/jackson-core-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson-core", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.datatype", "name": "jackson-datatype-jdk8", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-datatype-jdk8", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.0/jackson-datatype-jdk8-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.0/jackson-datatype-jdk8-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.datatype", "name": "jackson-datatype-jsr310", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-datatype-jsr310", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.0/jackson-datatype-jsr310-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.0/jackson-datatype-jsr310-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.core", "name": "jackson-databind", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-databind", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.19.0/jackson-databind-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.19.0/jackson-databind-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.module", "name": "jackson-module-scala_2.13", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-module-scala_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/module/jackson-module-scala_2.13/2.19.0/jackson-module-scala_2.13-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/module/jackson-module-scala_2.13/2.19.0/jackson-module-scala_2.13-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson-module-scala", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.dataformat", "name": "jackson-dataformat-smile", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-dataformat-smile", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.19.0/jackson-dataformat-smile-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.19.0/jackson-dataformat-smile-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson-dataformats-binary", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-protobuf-v3_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-protobuf-v3_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.reactivestreams", "name": "reactive-streams", "revision": "1.0.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "reactive-streams", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.reactive-streams.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT-0", "https://spdx.org/licenses/MIT-0.html"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "ssl-config-core_2.13", "revision": "0.6.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "ssl-config-core_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/ssl-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "jakarta.annotation", "name": "jakarta.annotation-api", "revision": "3.0.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jakarta.annotation-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/jakarta/annotation/jakarta.annotation-api/3.0.0/jakarta.annotation-api-3.0.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/jakarta/annotation/jakarta.annotation-api/3.0.0/jakarta.annotation-api-3.0.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://projects.eclipse.org/projects/ee4j.ca", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["EPL 2.0", "https://www.eclipse.org/legal/epl-2.0"], ["GPL2 w/ CPE", "https://www.gnu.org/software/classpath/license.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http-core_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http-core_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.jspecify", "name": "jspecify", "revision": "1.0.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jspecify", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jspecify/jspecify/1.0.0/jspecify-1.0.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/jspecify/jspecify/1.0.0/jspecify-1.0.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://jspecify.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.google.errorprone", "name": "error_prone_annotations", "revision": "2.36.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "error_prone_annotations", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.36.0/error_prone_annotations-2.36.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.36.0/error_prone_annotations-2.36.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.fasterxml.jackson.core", "name": "jackson-annotations", "revision": "2.19.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jackson-annotations", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.19.0/jackson-annotations-2.19.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-annotations/2.19.0/jackson-annotations-2.19.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/FasterXML/jackson", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.thoughtworks.paranamer", "name": "paranamer", "revision": "2.8.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "paranamer", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/thoughtworks/paranamer/paranamer/2.8.3/paranamer-2.8.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/thoughtworks/paranamer/paranamer/2.8.3/paranamer-2.8.3.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD", "https://opensource.org/license/bsd-3-clause"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-parsing_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-parsing_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.parboiled", "name": "parboiled_2.13", "revision": "2.5.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "parboiled_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://parboiled.org", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-eval_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko-testkit_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-core_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-pekko_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-chart_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}, {"module": {"organization": "com.netflix.atlas_v1", "name": "atlas-json_2.13", "revision": "1.8.0-SNAPSHOT", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/atlas", "extraAttributes": {"info.versionScheme": "semver-spec"}, "configurations": [{"name": "plugin"}, {"name": "pom"}, {"name": "test"}, {"name": "provided"}, {"name": "compile-internal"}, {"name": "docs"}, {"name": "optional"}, {"name": "compile"}, {"name": "test-internal"}, {"name": "scala-tool"}, {"name": "scala-doc-tool"}, {"name": "sources"}, {"name": "runtime"}, {"name": "runtime-internal"}], "licenses": [], "callers": []}], "details": []}], "stats": {"resolveTime": -1, "downloadTime": -1, "downloadSize": -1, "cached": false}, "stamps": {}}