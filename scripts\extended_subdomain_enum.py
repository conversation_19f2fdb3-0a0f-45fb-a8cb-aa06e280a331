#!/usr/bin/env python3
"""
Extended Subdomain Enumeration for Tinder
Uses multiple techniques including DNS brute force, certificate transparency, and common patterns
"""

import requests
import dns.resolver
import json
import sys
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import ssl
import socket

class ExtendedSubdomainEnum:
    def __init__(self, domain):
        self.domain = domain
        self.found_subdomains = set()
        self.alive_domains = []
        
        # Extended subdomain list for dating/social apps
        self.extended_subdomains = [
            # Core infrastructure
            'www', 'api', 'mobile', 'app', 'web', 'webapp', 'client',
            'admin', 'dashboard', 'panel', 'control', 'manage', 'console',
            'dev', 'development', 'test', 'testing', 'staging', 'stage', 'beta', 'alpha',
            'prod', 'production', 'live', 'release',
            
            # API versions and endpoints
            'api-v1', 'api-v2', 'api-v3', 'v1', 'v2', 'v3', 'v4',
            'mobile-api', 'app-api', 'rest-api', 'graphql', 'api-gateway',
            
            # CDN and static content
            'cdn', 'static', 'assets', 'media', 'images', 'img', 'photos',
            'upload', 'uploads', 'files', 'downloads', 'content',
            
            # Authentication and user management
            'auth', 'authentication', 'login', 'signin', 'signup', 'register',
            'sso', 'oauth', 'accounts', 'account', 'user', 'users', 'profile', 'profiles',
            'member', 'members', 'identity', 'id',
            
            # Dating app specific
            'match', 'matches', 'matching', 'swipe', 'like', 'likes', 'dislike',
            'chat', 'message', 'messages', 'messaging', 'conversation', 'talk',
            'date', 'dating', 'meet', 'connect', 'social',
            
            # Premium features
            'premium', 'plus', 'gold', 'platinum', 'subscription', 'billing',
            'payment', 'payments', 'checkout', 'purchase', 'shop', 'store',
            
            # Location and geo services
            'geo', 'location', 'maps', 'nearby', 'distance', 'gps',
            
            # Communication features
            'video', 'call', 'voice', 'stream', 'streaming', 'webrtc',
            'notification', 'notifications', 'push', 'alerts',
            
            # Analytics and tracking
            'analytics', 'stats', 'statistics', 'metrics', 'tracking', 'events',
            'logs', 'logging', 'monitor', 'monitoring',
            
            # Support and help
            'help', 'support', 'faq', 'docs', 'documentation', 'wiki',
            'blog', 'news', 'updates', 'changelog',
            
            # Security and compliance
            'security', 'privacy', 'legal', 'terms', 'policy', 'compliance',
            'verify', 'verification', 'validate', 'validation',
            
            # Infrastructure
            'mail', 'email', 'smtp', 'pop', 'imap', 'webmail',
            'ftp', 'sftp', 'ssh', 'vpn', 'remote', 'proxy',
            'lb', 'loadbalancer', 'cache', 'redis', 'db', 'database',
            
            # Mobile specific
            'm', 'mobile', 'touch', 'ios', 'android', 'app-store', 'play-store',
            
            # Regional/Language
            'en', 'us', 'uk', 'ca', 'au', 'de', 'fr', 'es', 'it', 'jp', 'kr', 'cn',
            'europe', 'asia', 'america', 'global', 'international',
            
            # Internal/Private
            'internal', 'private', 'secure', 'protected', 'restricted',
            'intranet', 'vpn', 'office', 'corp', 'corporate',
            
            # Development tools
            'jenkins', 'ci', 'cd', 'build', 'deploy', 'deployment',
            'git', 'gitlab', 'github', 'bitbucket', 'repo', 'repository',
            
            # Third-party integrations
            'facebook', 'fb', 'instagram', 'ig', 'snapchat', 'spotify',
            'google', 'apple', 'twitter', 'linkedin',
            
            # Common patterns
            'old', 'new', 'backup', 'bak', 'temp', 'tmp', 'archive',
            'legacy', 'deprecated', 'unused', 'test1', 'test2', 'demo'
        ]
        
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'X-HackerOne-Research': 'datafae'
        }
        
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def check_subdomain_exists(self, subdomain):
        """Check if subdomain exists via DNS and HTTP"""
        full_domain = f"{subdomain}.{self.domain}"
        
        try:
            # DNS check
            dns.resolver.resolve(full_domain, 'A')
            self.found_subdomains.add(full_domain)
            
            # HTTP/HTTPS check
            for protocol in ['https', 'http']:
                try:
                    url = f"{protocol}://{full_domain}"
                    response = self.session.get(url, timeout=8, allow_redirects=True)
                    
                    if response.status_code < 400:
                        domain_info = {
                            'domain': full_domain,
                            'url': url,
                            'status_code': response.status_code,
                            'title': self.extract_title(response.text),
                            'server': response.headers.get('Server', 'Unknown'),
                            'content_length': len(response.content),
                            'headers': dict(response.headers)
                        }
                        
                        self.alive_domains.append(domain_info)
                        print(f"[+] Found: {url} (Status: {response.status_code}) - {domain_info['title'][:50]}")
                        return True
                        
                except requests.exceptions.RequestException:
                    continue
                    
        except (dns.resolver.NXDOMAIN, dns.resolver.NoAnswer, Exception):
            pass
        
        return False
    
    def extract_title(self, html_content):
        """Extract title from HTML"""
        try:
            import re
            title_match = re.search(r'<title[^>]*>([^<]+)</title>', html_content, re.IGNORECASE)
            return title_match.group(1).strip() if title_match else "No Title"
        except:
            return "No Title"
    
    def check_certificate_transparency(self):
        """Check certificate transparency logs for subdomains"""
        try:
            # Using crt.sh API
            url = f"https://crt.sh/?q=%.{self.domain}&output=json"
            response = requests.get(url, timeout=15)
            
            if response.status_code == 200:
                certs = response.json()
                ct_subdomains = set()
                
                for cert in certs:
                    name_value = cert.get('name_value', '')
                    for name in name_value.split('\n'):
                        name = name.strip()
                        if name.endswith(f'.{self.domain}') and '*' not in name:
                            subdomain = name.replace(f'.{self.domain}', '').split('.')[0]
                            if subdomain and subdomain not in self.extended_subdomains:
                                ct_subdomains.add(subdomain)
                
                print(f"[*] Found {len(ct_subdomains)} additional subdomains from Certificate Transparency")
                return ct_subdomains
                
        except Exception as e:
            print(f"[!] Certificate Transparency check failed: {e}")
        
        return set()
    
    def run_enumeration(self):
        """Run the complete subdomain enumeration"""
        print(f"[*] Starting extended subdomain enumeration for {self.domain}")
        
        # Get additional subdomains from Certificate Transparency
        ct_subdomains = self.check_certificate_transparency()
        all_subdomains = set(self.extended_subdomains) | ct_subdomains
        
        print(f"[*] Testing {len(all_subdomains)} potential subdomains...")
        
        # Use threading for faster enumeration
        with ThreadPoolExecutor(max_workers=25) as executor:
            futures = [executor.submit(self.check_subdomain_exists, sub) for sub in all_subdomains]
            
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    pass
        
        print(f"\n[+] Enumeration complete!")
        print(f"[+] Found {len(self.found_subdomains)} total subdomains")
        print(f"[+] Found {len(self.alive_domains)} alive domains")
        
        return self.alive_domains
    
    def save_results(self):
        """Save results to files"""
        results = {
            'domain': self.domain,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'total_subdomains': len(self.found_subdomains),
            'alive_domains': len(self.alive_domains),
            'subdomains': list(self.found_subdomains),
            'alive_domain_details': self.alive_domains
        }
        
        with open(f'extended_recon_{self.domain}.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"[+] Results saved to extended_recon_{self.domain}.json")
        
        # Print interesting findings
        print("\n[*] Interesting findings:")
        for domain in self.alive_domains:
            if any(keyword in domain['domain'].lower() for keyword in ['staging', 'dev', 'test', 'admin', 'api']):
                print(f"  - {domain['domain']} ({domain['url']}) - {domain['title'][:50]}")

def main():
    if len(sys.argv) != 2:
        print("Usage: python extended_subdomain_enum.py <domain>")
        sys.exit(1)
    
    domain = sys.argv[1]
    enumerator = ExtendedSubdomainEnum(domain)
    enumerator.run_enumeration()
    enumerator.save_results()

if __name__ == "__main__":
    main()
