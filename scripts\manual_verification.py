#!/usr/bin/env python3
"""
Manual Verification Script for Tinder IDOR Findings
This script manually verifies potential IDOR vulnerabilities to eliminate false positives
"""

import requests
import json
import sys

def verify_idor_finding(url):
    """Manually verify an IDOR finding"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'X-HackerOne-Research': 'datafae',
        'Accept': 'application/json, text/plain, */*'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        
        print(f"\n=== Verifying: {url} ===")
        print(f"Status Code: {response.status_code}")
        print(f"Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
        print(f"Content Length: {len(response.content)} bytes")
        
        # Check if it's JSON
        try:
            json_data = response.json()
            print("Response Type: JSON")
            print(f"JSON Keys: {list(json_data.keys()) if isinstance(json_data, dict) else 'Not a dict'}")
            print(f"JSON Preview: {json.dumps(json_data, indent=2)[:300]}...")
            
            # Check for actual user data
            user_data_indicators = ['user_id', 'profile', 'photos', 'matches', 'messages']
            actual_data = any(key in json_data for key in user_data_indicators if isinstance(json_data, dict))
            
            if actual_data:
                print("🚨 CONFIRMED VULNERABILITY: Contains actual user data")
                return True
            else:
                print("✅ FALSE POSITIVE: No actual user data found")
                return False
                
        except json.JSONDecodeError:
            print("Response Type: HTML/Text")
            content = response.text.lower()
            
            # Check if it's just the standard Tinder homepage
            if 'tinder | dating, make friends' in content:
                print("✅ FALSE POSITIVE: Standard Tinder homepage (frontend router fallback)")
                return False
            elif len(response.text) < 1000:
                print(f"Response Preview: {response.text[:500]}...")
                print("✅ FALSE POSITIVE: Short response, likely error page")
                return False
            else:
                print(f"Response Preview: {response.text[:300]}...")
                print("❓ NEEDS MANUAL REVIEW: Substantial HTML content")
                return None
                
    except requests.exceptions.RequestException as e:
        print(f"❌ ERROR: {e}")
        return False

def main():
    # Test URLs from the IDOR findings
    test_urls = [
        "https://www.tinder.com/api/user/1",
        "https://www.tinder.com/api/user/2", 
        "https://www.tinder.com/api/users/1",
        "https://www.tinder.com/api/profile/1",
        "https://www.tinder.com/api/profiles/1",
        "https://staging.tinder.com/api/user/1",
        "https://staging.tinder.com/api/users/1"
    ]
    
    print("🔍 Manual Verification of Tinder IDOR Findings")
    print("=" * 60)
    
    confirmed_vulns = 0
    false_positives = 0
    needs_review = 0
    
    for url in test_urls:
        result = verify_idor_finding(url)
        
        if result is True:
            confirmed_vulns += 1
        elif result is False:
            false_positives += 1
        else:
            needs_review += 1
    
    print(f"\n" + "=" * 60)
    print("📊 VERIFICATION SUMMARY")
    print(f"Confirmed Vulnerabilities: {confirmed_vulns}")
    print(f"False Positives: {false_positives}")
    print(f"Needs Manual Review: {needs_review}")
    
    if confirmed_vulns == 0 and false_positives > 0:
        print("\n✅ CONCLUSION: All tested findings appear to be FALSE POSITIVES")
        print("The IDOR scanner detected standard HTML pages, not actual user data exposure.")
    elif confirmed_vulns > 0:
        print(f"\n🚨 CONCLUSION: {confirmed_vulns} CONFIRMED VULNERABILITIES found!")
    else:
        print("\n❓ CONCLUSION: Mixed results - manual review recommended")

if __name__ == "__main__":
    main()
