[0m[[0m[0mdebug[0m] [0m[0m[zinc] IncrementalCompile -----------[0m
[0m[[0m[0mdebug[0m] [0m[0mIncrementalCompile.incrementalCompile[0m
[0m[[0m[0mdebug[0m] [0m[0mprevious = Stamps for: 8 products, 5 sources, 8 libraries[0m
[0m[[0m[0mdebug[0m] [0m[0mcurrent source = Set(${BASE}/atlas-json/src/main/scala/com/netflix/atlas/json/JsonSupportSerializer.scala, ${BASE}/atlas-json/src/main/scala/com/netflix/atlas/json/JsonSupport.scala, ${BASE}/atlas-json/src/main/scala/com/netflix/atlas/json/Json.scala, ${BASE}/atlas-json/src/main/scala/com/netflix/atlas/json/JsonSupportSerializerModifier.scala, ${BASE}/atlas-json/src/main/scala/com/netflix/atlas/json/JsonParserHelper.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m> initialChanges = InitialChanges(Changes(added = Set(), removed = Set(), changed = Set(), unmodified = ...),Set(),Set(),API Changes: Set())[0m
[0m[[0m[0mdebug[0m] [0m[0mNo changes[0m
