atlas {

  core {
    model {
      step = 1 minute
    }

    db {
      class = "com.netflix.atlas.core.db.StaticDatabase"
      dataset = "alert"

      // How often to rebuild the index for the memory database
      rebuild-frequency = 40s

      // 1h
      block-size = 60

      // 24 * 14 (2wks at 1h)
      num-blocks = 336

      test-mode = false

      max-lines = 1024

      max-datapoints = 500000
    }

    vocabulary {
      //
      words = [
        // Sample entry:
        //{
        //  // Name of the operator, users would call `:$name`
        //  name = "square"
        //
        //  // Expression for this word
        //  body = ":dup,:mul"
        //
        //  // Examples to demonstrate the use of this operator
        //  examples = ["2"]
        //}
      ]

      // Keys available on the custom averages used at netflix
      nflx-keys = [
        "nf.account",
        "nf.app",
        "nf.ami",
        "nf.asg",
        "nf.cluster",
        "nf.stack",
        "nf.zone",
        "nf.region",
        "nf.node",
        "nf.ns",
        "nf.vmtype"
      ]

      // Helpers to compute an average based on a specified denominator query
      custom-averages = [
        // Sample entry:
        //{
        //  // Name of the operator, users would call `:$name`
        //  name = "node-avg"
        //
        //  // Query that is used for the denominator in the average
        //  base-query = "name,numInstances,:eq"
        //
        //  // Set of tags that are available for use on the denominator. These
        //  // will be used to extract common scope from the numerator query and
        //  // validate the group by behavior.
        //  keys = ["app", "cluster", "asg"]
        //}

        // Netflix averages:
        {
          name = "node-avg"
          base-query = "name,poller.asg.instance,:eq"
          keys = ${atlas.core.vocabulary.nflx-keys}
        },
        {
          name = "eureka-avg"
          base-query = "name,discovery.status,:eq,state,UP,:eq,:and"
          keys = ${atlas.core.vocabulary.nflx-keys}
        }
      ]
    }
  }
}
