{"cachedDescriptor": ".", "configurations": [{"configuration": {"name": "compile"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.google.code.findbugs", "name": "jsr305", "revision": "3.0.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsr305", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://findbugs.sourceforge.net/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-collection-compat_2.13", "revision": "2.13.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-collection-compat_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.typesafe.scala-logging", "name": "scala-logging_2.13", "revision": "3.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-logging_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/scala-logging", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0 License", "http://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.slf4j", "name": "slf4j-api", "revision": "2.0.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "slf4j-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.slf4j.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/license/mit"]], "callers": []}, {"module": {"organization": "com.netflix.spectator", "name": "spectator-api", "revision": "1.8.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spectator-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/spectator", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "config", "revision": "1.4.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "config", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http-testkit_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http-testkit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http-testkit_2.13/1.2.0/pekko-http-testkit_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http-testkit_2.13/1.2.0/pekko-http-testkit_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-stream-testkit_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-stream-testkit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-stream-testkit_2.13/1.1.3/pekko-stream-testkit_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-stream-testkit_2.13/1.1.3/pekko-stream-testkit_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-testkit_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-testkit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-testkit_2.13/1.1.3/pekko-testkit_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-testkit_2.13/1.1.3/pekko-testkit_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "munit_2.13", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "munit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/munit_2.13/1.1.1/munit_2.13-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/munit_2.13/1.1.1/munit_2.13-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-reflect", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-reflect", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-stream_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-stream_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-actor_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-actor_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "junit-interface", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "junit-interface", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/junit-interface/1.1.1/junit-interface-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/junit-interface/1.1.1/junit-interface-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "munit-diff_2.13", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "munit-diff_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/munit-diff_2.13/1.1.1/munit-diff_2.13-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/munit-diff_2.13/1.1.1/munit-diff_2.13-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "junit", "name": "junit", "revision": "4.13.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "junit", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/junit/junit/4.13.2/junit-4.13.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/junit/junit/4.13.2/junit-4.13.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://junit.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Eclipse Public License 1.0", "http://www.eclipse.org/legal/epl-v10.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http-core_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http-core_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-protobuf-v3_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-protobuf-v3_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.reactivestreams", "name": "reactive-streams", "revision": "1.0.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "reactive-streams", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.reactive-streams.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT-0", "https://spdx.org/licenses/MIT-0.html"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "ssl-config-core_2.13", "revision": "0.6.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "ssl-config-core_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/ssl-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "test-interface", "revision": "1.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "test-interface", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-sbt.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD", "https://github.com/sbt/test-interface/blob/master/LICENSE"]], "callers": []}, {"module": {"organization": "org.hamcrest", "name": "hamcrest-core", "revision": "1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "hamcrest-core", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["New BSD License", "http://www.opensource.org/licenses/bsd-license.php"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-parsing_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-parsing_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.parboiled", "name": "parboiled_2.13", "revision": "2.5.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "parboiled_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://parboiled.org", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}], "details": []}, {"configuration": {"name": "compile-internal"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.google.code.findbugs", "name": "jsr305", "revision": "3.0.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsr305", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://findbugs.sourceforge.net/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-collection-compat_2.13", "revision": "2.13.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-collection-compat_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.typesafe.scala-logging", "name": "scala-logging_2.13", "revision": "3.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-logging_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/scala-logging", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0 License", "http://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.slf4j", "name": "slf4j-api", "revision": "2.0.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "slf4j-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.slf4j.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/license/mit"]], "callers": []}, {"module": {"organization": "com.netflix.spectator", "name": "spectator-api", "revision": "1.8.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spectator-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/spectator", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "config", "revision": "1.4.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "config", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http-testkit_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http-testkit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http-testkit_2.13/1.2.0/pekko-http-testkit_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http-testkit_2.13/1.2.0/pekko-http-testkit_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-stream-testkit_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-stream-testkit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-stream-testkit_2.13/1.1.3/pekko-stream-testkit_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-stream-testkit_2.13/1.1.3/pekko-stream-testkit_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-testkit_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-testkit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-testkit_2.13/1.1.3/pekko-testkit_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-testkit_2.13/1.1.3/pekko-testkit_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "munit_2.13", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "munit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/munit_2.13/1.1.1/munit_2.13-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/munit_2.13/1.1.1/munit_2.13-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-reflect", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-reflect", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-stream_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-stream_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-actor_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-actor_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "junit-interface", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "junit-interface", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/junit-interface/1.1.1/junit-interface-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/junit-interface/1.1.1/junit-interface-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "munit-diff_2.13", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "munit-diff_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/munit-diff_2.13/1.1.1/munit-diff_2.13-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/munit-diff_2.13/1.1.1/munit-diff_2.13-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "junit", "name": "junit", "revision": "4.13.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "junit", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/junit/junit/4.13.2/junit-4.13.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/junit/junit/4.13.2/junit-4.13.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://junit.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Eclipse Public License 1.0", "http://www.eclipse.org/legal/epl-v10.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http-core_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http-core_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-protobuf-v3_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-protobuf-v3_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.reactivestreams", "name": "reactive-streams", "revision": "1.0.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "reactive-streams", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.reactive-streams.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT-0", "https://spdx.org/licenses/MIT-0.html"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "ssl-config-core_2.13", "revision": "0.6.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "ssl-config-core_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/ssl-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "test-interface", "revision": "1.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "test-interface", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-sbt.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD", "https://github.com/sbt/test-interface/blob/master/LICENSE"]], "callers": []}, {"module": {"organization": "org.hamcrest", "name": "hamcrest-core", "revision": "1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "hamcrest-core", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["New BSD License", "http://www.opensource.org/licenses/bsd-license.php"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-parsing_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-parsing_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.parboiled", "name": "parboiled_2.13", "revision": "2.5.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "parboiled_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://parboiled.org", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}], "details": []}, {"configuration": {"name": "docs"}, "modules": [], "details": []}, {"configuration": {"name": "optional"}, "modules": [], "details": []}, {"configuration": {"name": "plugin"}, "modules": [], "details": []}, {"configuration": {"name": "pom"}, "modules": [], "details": []}, {"configuration": {"name": "provided"}, "modules": [], "details": []}, {"configuration": {"name": "runtime"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.google.code.findbugs", "name": "jsr305", "revision": "3.0.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsr305", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://findbugs.sourceforge.net/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-collection-compat_2.13", "revision": "2.13.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-collection-compat_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.typesafe.scala-logging", "name": "scala-logging_2.13", "revision": "3.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-logging_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/scala-logging", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0 License", "http://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.slf4j", "name": "slf4j-api", "revision": "2.0.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "slf4j-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.slf4j.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/license/mit"]], "callers": []}, {"module": {"organization": "com.netflix.spectator", "name": "spectator-api", "revision": "1.8.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spectator-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/spectator", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "config", "revision": "1.4.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "config", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http-testkit_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http-testkit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http-testkit_2.13/1.2.0/pekko-http-testkit_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http-testkit_2.13/1.2.0/pekko-http-testkit_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-stream-testkit_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-stream-testkit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-stream-testkit_2.13/1.1.3/pekko-stream-testkit_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-stream-testkit_2.13/1.1.3/pekko-stream-testkit_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-testkit_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-testkit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-testkit_2.13/1.1.3/pekko-testkit_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-testkit_2.13/1.1.3/pekko-testkit_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "munit_2.13", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "munit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/munit_2.13/1.1.1/munit_2.13-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/munit_2.13/1.1.1/munit_2.13-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-reflect", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-reflect", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-stream_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-stream_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-actor_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-actor_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "junit-interface", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "junit-interface", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/junit-interface/1.1.1/junit-interface-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/junit-interface/1.1.1/junit-interface-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "munit-diff_2.13", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "munit-diff_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/munit-diff_2.13/1.1.1/munit-diff_2.13-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/munit-diff_2.13/1.1.1/munit-diff_2.13-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "junit", "name": "junit", "revision": "4.13.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "junit", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/junit/junit/4.13.2/junit-4.13.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/junit/junit/4.13.2/junit-4.13.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://junit.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Eclipse Public License 1.0", "http://www.eclipse.org/legal/epl-v10.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http-core_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http-core_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-protobuf-v3_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-protobuf-v3_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.reactivestreams", "name": "reactive-streams", "revision": "1.0.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "reactive-streams", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.reactive-streams.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT-0", "https://spdx.org/licenses/MIT-0.html"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "ssl-config-core_2.13", "revision": "0.6.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "ssl-config-core_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/ssl-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "test-interface", "revision": "1.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "test-interface", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-sbt.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD", "https://github.com/sbt/test-interface/blob/master/LICENSE"]], "callers": []}, {"module": {"organization": "org.hamcrest", "name": "hamcrest-core", "revision": "1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "hamcrest-core", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["New BSD License", "http://www.opensource.org/licenses/bsd-license.php"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-parsing_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-parsing_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.parboiled", "name": "parboiled_2.13", "revision": "2.5.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "parboiled_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://parboiled.org", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}], "details": []}, {"configuration": {"name": "runtime-internal"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.google.code.findbugs", "name": "jsr305", "revision": "3.0.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsr305", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://findbugs.sourceforge.net/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-collection-compat_2.13", "revision": "2.13.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-collection-compat_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.typesafe.scala-logging", "name": "scala-logging_2.13", "revision": "3.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-logging_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/scala-logging", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0 License", "http://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.slf4j", "name": "slf4j-api", "revision": "2.0.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "slf4j-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.slf4j.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/license/mit"]], "callers": []}, {"module": {"organization": "com.netflix.spectator", "name": "spectator-api", "revision": "1.8.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spectator-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/spectator", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "config", "revision": "1.4.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "config", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http-testkit_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http-testkit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http-testkit_2.13/1.2.0/pekko-http-testkit_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http-testkit_2.13/1.2.0/pekko-http-testkit_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-stream-testkit_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-stream-testkit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-stream-testkit_2.13/1.1.3/pekko-stream-testkit_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-stream-testkit_2.13/1.1.3/pekko-stream-testkit_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-testkit_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-testkit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-testkit_2.13/1.1.3/pekko-testkit_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-testkit_2.13/1.1.3/pekko-testkit_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "munit_2.13", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "munit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/munit_2.13/1.1.1/munit_2.13-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/munit_2.13/1.1.1/munit_2.13-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-reflect", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-reflect", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-stream_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-stream_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-actor_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-actor_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "junit-interface", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "junit-interface", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/junit-interface/1.1.1/junit-interface-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/junit-interface/1.1.1/junit-interface-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "munit-diff_2.13", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "munit-diff_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/munit-diff_2.13/1.1.1/munit-diff_2.13-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/munit-diff_2.13/1.1.1/munit-diff_2.13-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "junit", "name": "junit", "revision": "4.13.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "junit", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/junit/junit/4.13.2/junit-4.13.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/junit/junit/4.13.2/junit-4.13.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://junit.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Eclipse Public License 1.0", "http://www.eclipse.org/legal/epl-v10.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http-core_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http-core_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-protobuf-v3_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-protobuf-v3_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.reactivestreams", "name": "reactive-streams", "revision": "1.0.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "reactive-streams", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.reactive-streams.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT-0", "https://spdx.org/licenses/MIT-0.html"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "ssl-config-core_2.13", "revision": "0.6.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "ssl-config-core_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/ssl-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "test-interface", "revision": "1.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "test-interface", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-sbt.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD", "https://github.com/sbt/test-interface/blob/master/LICENSE"]], "callers": []}, {"module": {"organization": "org.hamcrest", "name": "hamcrest-core", "revision": "1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "hamcrest-core", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["New BSD License", "http://www.opensource.org/licenses/bsd-license.php"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-parsing_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-parsing_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.parboiled", "name": "parboiled_2.13", "revision": "2.5.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "parboiled_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://parboiled.org", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}], "details": []}, {"configuration": {"name": "scala-doc-tool"}, "modules": [], "details": []}, {"configuration": {"name": "scala-tool"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-compiler", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-compiler/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-compiler", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.13.16/scala-compiler-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-compiler/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.13.16/scala-compiler-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-compiler/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-compiler", "revision": "2.13.16", "configurations": "optional", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-compiler/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-compiler", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.13.16/scala-compiler-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-compiler/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.13.16/scala-compiler-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-compiler/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.16", "configurations": "optional", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-reflect", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-reflect", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "io.github.java-diff-utils", "name": "java-diff-utils", "revision": "4.15", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "java-diff-utils", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/io/github/java-diff-utils/java-diff-utils/4.15/java-diff-utils-4.15.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/io/github/java-diff-utils/java-diff-utils/4.15/java-diff-utils-4.15.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.jline", "name": "jline", "revision": "3.27.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jline", "type": "jar", "extension": "jar", "classifier": "jdk8", "configurations": [], "url": "https://repo1.maven.org/maven2/org/jline/jline/3.27.1/jline-3.27.1-jdk8.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/jline/jline/3.27.1/jline-3.27.1-jdk8.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The BSD License", "https://opensource.org/licenses/BSD-3-Clause"]], "callers": []}], "details": []}, {"configuration": {"name": "sources"}, "modules": [], "details": []}, {"configuration": {"name": "test"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.google.code.findbugs", "name": "jsr305", "revision": "3.0.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsr305", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://findbugs.sourceforge.net/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-collection-compat_2.13", "revision": "2.13.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-collection-compat_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.typesafe.scala-logging", "name": "scala-logging_2.13", "revision": "3.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-logging_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/scala-logging", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0 License", "http://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.slf4j", "name": "slf4j-api", "revision": "2.0.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "slf4j-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.slf4j.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/license/mit"]], "callers": []}, {"module": {"organization": "com.netflix.spectator", "name": "spectator-api", "revision": "1.8.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spectator-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/spectator", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "config", "revision": "1.4.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "config", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "munit_2.13", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "munit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/munit_2.13/1.1.1/munit_2.13-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/munit_2.13/1.1.1/munit_2.13-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http-testkit_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http-testkit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http-testkit_2.13/1.2.0/pekko-http-testkit_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http-testkit_2.13/1.2.0/pekko-http-testkit_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-stream-testkit_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-stream-testkit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-stream-testkit_2.13/1.1.3/pekko-stream-testkit_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-stream-testkit_2.13/1.1.3/pekko-stream-testkit_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-testkit_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-testkit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-testkit_2.13/1.1.3/pekko-testkit_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-testkit_2.13/1.1.3/pekko-testkit_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-reflect", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-reflect", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "junit-interface", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "junit-interface", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/junit-interface/1.1.1/junit-interface-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/junit-interface/1.1.1/junit-interface-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "munit-diff_2.13", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "munit-diff_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/munit-diff_2.13/1.1.1/munit-diff_2.13-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/munit-diff_2.13/1.1.1/munit-diff_2.13-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "junit", "name": "junit", "revision": "4.13.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "junit", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/junit/junit/4.13.2/junit-4.13.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/junit/junit/4.13.2/junit-4.13.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://junit.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Eclipse Public License 1.0", "http://www.eclipse.org/legal/epl-v10.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-stream_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-stream_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-actor_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-actor_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "test-interface", "revision": "1.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "test-interface", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-sbt.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD", "https://github.com/sbt/test-interface/blob/master/LICENSE"]], "callers": []}, {"module": {"organization": "org.hamcrest", "name": "hamcrest-core", "revision": "1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "hamcrest-core", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["New BSD License", "http://www.opensource.org/licenses/bsd-license.php"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http-core_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http-core_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-protobuf-v3_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-protobuf-v3_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.reactivestreams", "name": "reactive-streams", "revision": "1.0.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "reactive-streams", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.reactive-streams.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT-0", "https://spdx.org/licenses/MIT-0.html"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "ssl-config-core_2.13", "revision": "0.6.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "ssl-config-core_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/ssl-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-parsing_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-parsing_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.parboiled", "name": "parboiled_2.13", "revision": "2.5.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "parboiled_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://parboiled.org", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}], "details": []}, {"configuration": {"name": "test-internal"}, "modules": [{"module": {"organization": "org.scala-lang", "name": "scala-library", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-library", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.13.16/scala-library-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.google.code.findbugs", "name": "jsr305", "revision": "3.0.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "jsr305", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://findbugs.sourceforge.net/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.scala-lang.modules", "name": "scala-collection-compat_2.13", "revision": "2.13.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-collection-compat_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.13/2.13.0/scala-collection-compat_2.13-2.13.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-lang.org/", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "com.typesafe.scala-logging", "name": "scala-logging_2.13", "revision": "3.9.5", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-logging_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/scala-logging", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache 2.0 License", "http://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.slf4j", "name": "slf4j-api", "revision": "2.0.17", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "slf4j-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.slf4j.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT", "https://opensource.org/license/mit"]], "callers": []}, {"module": {"organization": "com.netflix.spectator", "name": "spectator-api", "revision": "1.8.14", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "spectator-api", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/netflix/spectator/spectator-api/1.8.14/spectator-api-1.8.14.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/Netflix/spectator", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["The Apache Software License, Version 2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "config", "revision": "1.4.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "config", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "munit_2.13", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "munit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/munit_2.13/1.1.1/munit_2.13-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/munit_2.13/1.1.1/munit_2.13-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http-testkit_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http-testkit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http-testkit_2.13/1.2.0/pekko-http-testkit_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http-testkit_2.13/1.2.0/pekko-http-testkit_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-stream-testkit_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-stream-testkit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-stream-testkit_2.13/1.1.3/pekko-stream-testkit_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-stream-testkit_2.13/1.1.3/pekko-stream-testkit_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-testkit_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-testkit_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-testkit_2.13/1.1.3/pekko-testkit_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-testkit_2.13/1.1.3/pekko-testkit_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.scala-lang", "name": "scala-reflect", "revision": "2.13.16", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "scala-reflect", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.13.16/scala-reflect-2.13.16.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://www.scala-lang.org/", "extraAttributes": {"info.apiURL": "https://www.scala-lang.org/api/2.13.16/scala-reflect/"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "junit-interface", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "junit-interface", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/junit-interface/1.1.1/junit-interface-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/junit-interface/1.1.1/junit-interface-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "org.scalameta", "name": "munit-diff_2.13", "revision": "1.1.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "munit-diff_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scalameta/munit-diff_2.13/1.1.1/munit-diff_2.13-1.1.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scalameta/munit-diff_2.13/1.1.1/munit-diff_2.13-1.1.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/scalameta/munit", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0"]], "callers": []}, {"module": {"organization": "junit", "name": "junit", "revision": "4.13.2", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "junit", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/junit/junit/4.13.2/junit-4.13.2.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/junit/junit/4.13.2/junit-4.13.2.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://junit.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Eclipse Public License 1.0", "http://www.eclipse.org/legal/epl-v10.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http_2.13/1.2.0/pekko-http_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-stream_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-stream_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-stream_2.13/1.1.3/pekko-stream_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-actor_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-actor_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-actor_2.13/1.1.3/pekko-actor_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.scala-sbt", "name": "test-interface", "revision": "1.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "test-interface", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.scala-sbt.org", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["BSD", "https://github.com/sbt/test-interface/blob/master/LICENSE"]], "callers": []}, {"module": {"organization": "org.hamcrest", "name": "hamcrest-core", "revision": "1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "hamcrest-core", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["New BSD License", "http://www.opensource.org/licenses/bsd-license.php"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-http-core_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-http-core_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-http-core_2.13/1.2.0/pekko-http-core_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-protobuf-v3_2.13", "revision": "1.1.3", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-protobuf-v3_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-protobuf-v3_2.13/1.1.3/pekko-protobuf-v3_2.13-1.1.3.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://pekko.apache.org/", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko/1.1.3", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.reactivestreams", "name": "reactive-streams", "revision": "1.0.4", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "reactive-streams", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://www.reactive-streams.org/", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["MIT-0", "https://spdx.org/licenses/MIT-0.html"]], "callers": []}, {"module": {"organization": "com.typesafe", "name": "ssl-config-core_2.13", "revision": "0.6.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "ssl-config-core_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar", "extraAttributes": {}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.13/0.6.1/ssl-config-core_2.13-0.6.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "https://github.com/lightbend/ssl-config", "extraAttributes": {}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}, {"module": {"organization": "org.apache.pekko", "name": "pekko-parsing_2.13", "revision": "1.2.0", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "pekko-parsing_2.13", "type": "jar", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar", "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/apache/pekko/pekko-parsing_2.13/1.2.0/pekko-parsing_2.13-1.2.0.jar"]], "missingArtifacts": [], "evicted": false, "extraAttributes": {"info.apiURL": "https://pekko.apache.org/api/pekko-http/1.2.0/", "info.versionScheme": "semver-spec"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "https://www.apache.org/licenses/LICENSE-2.0.html"]], "callers": []}, {"module": {"organization": "org.parboiled", "name": "parboiled_2.13", "revision": "2.5.1", "configurations": "default", "isChanging": false, "isTransitive": true, "isForce": false, "explicitArtifacts": [], "inclusions": [], "exclusions": [], "extraAttributes": {"info.versionScheme": "early-semver"}, "crossVersion": {"type": "Disabled"}}, "artifacts": [[{"name": "parboiled_2.13", "type": "bundle", "extension": "jar", "configurations": [], "url": "https://repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar", "extraAttributes": {"info.versionScheme": "early-semver"}, "allowInsecureProtocol": false}, "file:///C:/Users/<USER>/AppData/Local/Coursier/Cache/v1/https/repo1.maven.org/maven2/org/parboiled/parboiled_2.13/2.5.1/parboiled_2.13-2.5.1.jar"]], "missingArtifacts": [], "evicted": false, "homepage": "http://parboiled.org", "extraAttributes": {"info.versionScheme": "early-semver"}, "configurations": [{"name": "test"}, {"name": "optional"}, {"name": "compile"}, {"name": "default"}, {"name": "runtime"}], "licenses": [["Apache-2.0", "http://www.apache.org/licenses/LICENSE-2.0.txt"]], "callers": []}], "details": []}], "stats": {"resolveTime": -1, "downloadTime": -1, "downloadSize": -1, "cached": false}, "stamps": {}}