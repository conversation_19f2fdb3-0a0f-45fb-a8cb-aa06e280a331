[0m[[0m[0mdebug[0m] [0m[0mCreated transactional ClassFileManager with tempDir = C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-spring-webapi\target\scala-2.13\classes.bak[0m
[0m[[0m[0mdebug[0m] [0m[0mRemoving the temporary directory used for backing up class files: C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-spring-webapi\target\scala-2.13\classes.bak[0m
