["sbt.Task[scala.collection.Seq[java.nio.file.Path]]", ["C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\SubscriptionManager$ConcurrentSet.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\ExpressionSplitter$$anonfun$$nestedInanonfun$parse$1$2.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\SubscribeApi$SubscribeRequest$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\SubscribeApi$ErrorMsg.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\EvaluateApi$Item$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\SubscribeApi$SubscribeRequest.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\StreamsApi.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\StreamMetadata$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\ExpressionApi.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\ApiSettings.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\ExpressionApi$EncodedExpressions$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\WebSocketSessionManager$$anon$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\ExpressionSplitter.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\SubscriptionManager$StreamInfo.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\WebSocketSessionManager.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\package$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\EvaluateApi$EvaluateRequest$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\ExpressionMetadata$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\SubscribeApi$Errors.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\Subscription$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\ApiSettings$$anonfun$$lessinit$greater$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\ExpressionApi$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\ExpressionSplitter$$anonfun$2.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\SubscriptionManager$StreamSummary.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\SubscriptionManager.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\QueueHandler.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\ExpressionApi$Return$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\StreamMetadata.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\ApiSettings$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\ExpressionApi$ExpressionsCache.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\SubscribeApi$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\ExpressionApi$Return.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\SubscribeApi.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\ExpressionSplitter$$anonfun$$nestedInanonfun$parse$1$3.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\EvaluateApi.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\Subscription.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\ExpressionSplitter$DataExprMeta$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\ExpressionMetadata.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\SubscriptionManager$StreamInfo$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\StartupDelayService.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\ExpressionSplitter$$anonfun$$nestedInanonfun$parse$1$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\StreamSubscriptionManager.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\EvaluateApi$Item.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\SubscribeApi$Errors$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\EvaluateApi$EvaluateRequest.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\ExpressionSplitter$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\ExpressionSplitter$$anonfun$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\ExpressionApi$EncodedExpressions.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\EvaluateApi$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\SubscriptionManager$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\SubscriptionManager$StreamSummary$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\ExpressionApi$CacheEntry.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\SubscribeApi$ErrorMsg$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\package.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\lwcapi\\ExpressionSplitter$DataExprMeta.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-lwcapi\\target\\scala-2.13\\zinc\\inc_compile_2.13.zip"]]