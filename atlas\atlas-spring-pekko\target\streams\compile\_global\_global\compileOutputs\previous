["sbt.Task[scala.collection.Seq[java.nio.file.Path]]", ["C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-spring-pekko\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\pekko\\ActorSystemService.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-spring-pekko\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\pekko\\MaterializerService.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-spring-pekko\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\pekko\\PekkoConfiguration.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-spring-pekko\\target\\scala-2.13\\zinc\\inc_compile_2.13.zip"]]