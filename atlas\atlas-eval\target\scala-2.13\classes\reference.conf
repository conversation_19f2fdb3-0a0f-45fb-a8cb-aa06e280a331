
atlas.eval {
  stream {
    unique-timeout = 60s

    backends = [
      {
        host = "localhost"
        edda-uri = "http://localhost:7102/api/v1/autoScalingGroups/local-dev:7001"
      }
    ]

    // Pattern to use for backend URIs
    backend-uri-pattern = "ws://{ip}:{port}"

    // Number of buffers to use for the time grouping. More buffers means that data has
    // more time to accumulate and there is less chance of data being dropped because it
    // is too old
    num-buffers = 1

    limits {
      // Maximum number of raw input datapoints for a data expr. Defaults to Integer.MaxValue
      max-input-datapoints = 2147483647
      // Maximum number of datapoints resulting from a group by. Defaults to Integer.MaxValue
      max-intermediate-datapoints = 2147483647

      // Maximum step size allowed for streaming. Large step sizes require a large amount of time
      // to accumulate data and could complicate deployments if not data loss is desirable. Limit
      // to avoid usage.
      max-step = 60s
    }

    // Broad tag keys that should be ignored for the purposes of dropping expensive queries
    // that will be prohibitive to match. These are typically tags that will be applied to
    // everything within a given deployment scope.
    ignored-tag-keys = [
      "nf.account",
      "nf.region"
    ]

    // Which version of the LWC server API to use
    lwcapi-version = 2

    // Rewriting for data sources. Can be enabled to perform rewrites on the data source.
    rewrite {
      // Supported values:
      // - `none`: do not do any rewrites
      // - `config`: simple host mapping based on config settings
      mode = "none"
    }

    // Should no-data messages be enabled. For some use-cases they may not be desirable, only
    // emit them if set to true.
    enable-no-data-messages = true

    // Should subscription messages be compressed.
    compress-sub-messages = true
  }

  graph {
    step = ${atlas.core.model.step}
    block-size = ${atlas.core.db.block-size}

    start-time = e-3h
    end-time = now
    timezone = US/Pacific
    width = 700
    height = 300
    theme = "light"

    // Settings for light theme
    light {
      palette {
        primary = "armytage"
        offset = "bw"
      }
      named-colors = {
        blue1 = "6BAED6"
        blue2 = "2171B5"
        blue3 = "08306B"

        gray1 = "888888"
        gray2 = "444444"
        gray3 = "000000"

        green1 = "74C476"
        green2 = "238B45"
        green3 = "00441B"

        orange1 = "FD8D3C"
        orange2 = "D94801"
        orange3 = "7F2704"

        purple1 = "9E9AC8"
        purple2 = "6A51A3"
        purple3 = "3F007D"

        red1 = "FB6A4A"
        red2 = "CB181D"
        red3 = "67000D"
      }
    }

    // Settings for dark theme
    dark {
      palette {
        primary = "light24"
        offset = "lightgray"
      }
      named-colors = {
        blue1 = "C6DBEF"
        blue2 = "6BAED6"
        blue3 = "2171B5"

        gray1 = "FFFFFF"
        gray2 = "BBBBBB"
        gray3 = "777777"

        green1 = "C7E9C0"
        green2 = "74C476"
        green3 = "238B45"

        orange1 = "FDD0A2"
        orange2 = "FD8D3C"
        orange3 = "D94801"

        purple1 = "DADAEB"
        purple2 = "9E9AC8"
        purple3 = "6A51A3"

        red1 = "FCBBA1"
        red2 = "FB6A4A"
        red3 = "CB181D"
      }
    }

    // Don't permit more that 1440 datapoints (1 day at minute resolution) for a single graph
    max-datapoints = 1440

    // Set of output formats to support via the graph API
    engines = [
      "com.netflix.atlas.chart.CommaSepGraphEngine",
      "com.netflix.atlas.chart.TabSepGraphEngine",
      "com.netflix.atlas.chart.JsonGraphEngine",
      "com.netflix.atlas.chart.StatsJsonGraphEngine",
      "com.netflix.atlas.chart.StdJsonGraphEngine",
      "com.netflix.atlas.chart.V2JsonGraphEngine",
      "com.netflix.atlas.chart.DefaultGraphEngine"
    ]

    // Vocabulary to use for the graph API. The value can be "default" or a class representing
    // the vocabulary to use.
    vocabulary = "default"

    // If set to true, the graph uri will be encoded as a Source iTXt field in the generated
    // png image. This can be useful for debugging and possibly other tooling, but increases
    // the image sizes and may not be desirable if the image may be shared externally.
    png-metadata-enabled = false

    // Pattern to use to detect that a user-agent is a web-browser
    browser-agent-pattern = "mozilla|msie|gecko|chrome|opera|webkit"

    // If set to true, then it will try to generate a simple legend for the set of expressions
    // on the graph. By default the legend will just summarize the expression which is often
    // long and hard to read for users.
    simple-legends-enabled = true
  }

  host-rewrite {
    // Default is a pattern that doesn't match anything
    pattern = "$^"
    key = ""
  }
}
