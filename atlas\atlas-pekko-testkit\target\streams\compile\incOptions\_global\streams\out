[0m[[0m[0mdebug[0m] [0m[0mCreated transactional ClassFileManager with tempDir = C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-pekko-testkit\target\scala-2.13\classes.bak[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to delete class files:[0m
[0m[[0m[0mdebug[0m] [0m[0mWe backup class files:[0m
[0m[[0m[0mdebug[0m] [0m[0mCreated transactional ClassFileManager with tempDir = C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-pekko-testkit\target\scala-2.13\classes.bak[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to delete class files:[0m
[0m[[0m[0mdebug[0m] [0m[0mWe backup class files:[0m
[0m[[0m[0mdebug[0m] [0m[0mRolling back changes to class files.[0m
[0m[[0m[0mdebug[0m] [0m[0mRemoving generated classes:[0m
[0m[[0m[0mdebug[0m] [0m[0mRestoring class files: [0m
[0m[[0m[0mdebug[0m] [0m[0mRemoving the temporary directory used for backing up class files: C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-pekko-testkit\target\scala-2.13\classes.bak[0m
