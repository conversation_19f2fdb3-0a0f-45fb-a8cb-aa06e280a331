[0m[[0m[0mdebug[0m] [0m[0m[zinc] IncrementalCompile -----------[0m
[0m[[0m[0mdebug[0m] [0m[0mIncrementalCompile.incrementalCompile[0m
[0m[[0m[0mdebug[0m] [0m[0mprevious = Stamps for: 154 products, 49 sources, 13 libraries[0m
[0m[[0m[0mdebug[0m] [0m[0mcurrent source = Set(${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/model/LwcDataExpr.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/model/LwcEvent.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/model/LwcDatapoint.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/stream/SyntheticDataSource.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/stream/EddaSource.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/stream/DataSourceLogger.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/graph/GraphConfig.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/stream/OnUpstreamFinish.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/model/LwcSubscription.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/stream/package.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/model/EventMessage.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/model/AggrDatapoint.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/model/ExprType.java, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/model/LwcSubscriptionV2.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/model/LwcDiagnosticMessage.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/model/DatapointsTuple.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/model/ChunkData.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/stream/EvalDataRateCollector.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/stream/ExprInterpreter.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/stream/EvaluatorImpl.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/model/EvalDataRate.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/stream/Evaluator.java, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/stream/EddaGroupsLookup.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/graph/ImageFlags.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/stream/EvaluationFlows.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/stream/LwcToAggrDatapoint.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/model/LwcHeartbeat.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/model/LineStyleMetadata.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/graph/Axis.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/model/LwcExpression.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/graph/DefaultSettings.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/stream/StreamRef.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/util/IdParamSanitizer.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/stream/StreamContext.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/stream/FillRemovedKeysWith.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/util/SortedTagMapDeserializer.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/model/TimeGroupsTuple.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/stream/ReplayLogging.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/model/TimeGroup.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/stream/SourceRef.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/stream/TimeGrouped.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/model/LwcMessages.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/graph/package.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/stream/DataSourceRewriter.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/graph/Grapher.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/model/TimeSeriesMessage.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/util/HostRewriter.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/stream/FinalExprEval.scala, ${BASE}/atlas-eval/src/main/scala/com/netflix/atlas/eval/graph/SimpleLegends.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m> initialChanges = InitialChanges(Changes(added = Set(), removed = Set(), changed = Set(), unmodified = ...),Set(),Set(),API Changes: Set())[0m
[0m[[0m[0mdebug[0m] [0m[0mNo changes[0m
