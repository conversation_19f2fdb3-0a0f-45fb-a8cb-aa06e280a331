#!/usr/bin/env python3
"""
Tinder Information Disclosure Tester
Tests for sensitive information exposure through various vectors
"""

import requests
import json
import time
import sys
import re
from urllib.parse import urljoin

class TinderInfoDisclosureTester:
    def __init__(self, base_urls):
        self.base_urls = base_urls if isinstance(base_urls, list) else [base_urls]
        self.session = requests.Session()
        self.vulnerabilities = []
        
        # Headers with bug bounty research identifier
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'X-HackerOne-Research': 'datafae',
            'Accept': 'application/json, text/html, */*',
            'Accept-Language': 'en-US,en;q=0.9'
        }
        
        self.session.headers.update(self.headers)
        
        # Information disclosure endpoints
        self.info_endpoints = [
            # Configuration and debug endpoints
            '/api/config',
            '/api/configuration',
            '/api/settings',
            '/api/debug',
            '/api/health',
            '/api/status',
            '/api/version',
            '/api/info',
            '/api/env',
            '/api/environment',
            
            # Admin and internal endpoints
            '/api/admin',
            '/api/internal',
            '/api/private',
            '/api/test',
            '/api/dev',
            '/api/staging',
            
            # Documentation and help
            '/api/docs',
            '/api/documentation',
            '/api/help',
            '/api/swagger',
            '/api/openapi',
            '/api/graphql',
            
            # Backup and temporary files
            '/api/backup',
            '/api/bak',
            '/api/temp',
            '/api/tmp',
            '/api/old',
            
            # Error pages and logs
            '/api/error',
            '/api/errors',
            '/api/logs',
            '/api/log',
            '/api/trace',
            
            # Common files that might expose info
            '/.env',
            '/config.json',
            '/package.json',
            '/composer.json',
            '/web.config',
            '/app.config',
            '/.git/config',
            '/robots.txt',
            '/sitemap.xml',
            '/.well-known/security.txt',
            
            # Source maps and debug files
            '/static/js/main.js.map',
            '/assets/js/app.js.map',
            '/js/bundle.js.map',
            '/webpack.config.js',
            
            # Database and backup files
            '/database.sql',
            '/backup.sql',
            '/dump.sql',
            '/users.sql'
        ]
        
        # Sensitive information patterns
        self.sensitive_patterns = {
            'api_keys': [
                r'api[_-]?key["\s]*[:=]["\s]*([a-zA-Z0-9_-]{20,})',
                r'apikey["\s]*[:=]["\s]*([a-zA-Z0-9_-]{20,})',
                r'api[_-]?secret["\s]*[:=]["\s]*([a-zA-Z0-9_-]{20,})'
            ],
            'database_credentials': [
                r'database[_-]?url["\s]*[:=]["\s]*([^\s"]+)',
                r'db[_-]?password["\s]*[:=]["\s]*([^\s"]+)',
                r'mysql[_-]?password["\s]*[:=]["\s]*([^\s"]+)',
                r'postgres[_-]?password["\s]*[:=]["\s]*([^\s"]+)'
            ],
            'aws_credentials': [
                r'aws[_-]?access[_-]?key["\s]*[:=]["\s]*([A-Z0-9]{20})',
                r'aws[_-]?secret[_-]?key["\s]*[:=]["\s]*([a-zA-Z0-9/+=]{40})',
                r'aws[_-]?session[_-]?token["\s]*[:=]["\s]*([a-zA-Z0-9/+=]+)'
            ],
            'jwt_secrets': [
                r'jwt[_-]?secret["\s]*[:=]["\s]*([a-zA-Z0-9_-]{20,})',
                r'token[_-]?secret["\s]*[:=]["\s]*([a-zA-Z0-9_-]{20,})',
                r'session[_-]?secret["\s]*[:=]["\s]*([a-zA-Z0-9_-]{20,})'
            ],
            'email_credentials': [
                r'smtp[_-]?password["\s]*[:=]["\s]*([^\s"]+)',
                r'mail[_-]?password["\s]*[:=]["\s]*([^\s"]+)',
                r'email[_-]?password["\s]*[:=]["\s]*([^\s"]+)'
            ],
            'internal_urls': [
                r'https?://[a-zA-Z0-9.-]*\.internal[^\s"]*',
                r'https?://[a-zA-Z0-9.-]*\.local[^\s"]*',
                r'https?://localhost[:\d]*[^\s"]*',
                r'https?://127\.0\.0\.1[:\d]*[^\s"]*'
            ],
            'private_keys': [
                r'-----BEGIN [A-Z ]+PRIVATE KEY-----',
                r'-----BEGIN RSA PRIVATE KEY-----',
                r'-----BEGIN OPENSSH PRIVATE KEY-----'
            ]
        }
    
    def print_status(self, message, level="INFO"):
        colors = {"INFO": "\033[94m", "SUCCESS": "\033[92m", "WARNING": "\033[93m", "ERROR": "\033[91m", "RESET": "\033[0m"}
        print(f"{colors.get(level, '')}{message}{colors['RESET']}")
    
    def analyze_response_for_sensitive_info(self, response, url):
        """Analyze response content for sensitive information"""
        content = response.text
        found_sensitive = {}
        
        for category, patterns in self.sensitive_patterns.items():
            matches = []
            for pattern in patterns:
                found = re.findall(pattern, content, re.IGNORECASE)
                if found:
                    matches.extend(found)
            
            if matches:
                found_sensitive[category] = matches
        
        if found_sensitive:
            vulnerability = {
                'type': 'Information Disclosure - Sensitive Data Exposure',
                'url': url,
                'status_code': response.status_code,
                'content_type': response.headers.get('Content-Type', 'Unknown'),
                'sensitive_data': found_sensitive,
                'severity': self.calculate_severity(found_sensitive),
                'content_preview': content[:500] + '...' if len(content) > 500 else content
            }
            
            self.vulnerabilities.append(vulnerability)
            self.print_status(f"[!] Sensitive data found: {url} - {list(found_sensitive.keys())}", "ERROR")
            return True
        
        return False
    
    def calculate_severity(self, sensitive_data):
        """Calculate severity based on type of sensitive data found"""
        critical_types = ['aws_credentials', 'database_credentials', 'private_keys']
        high_types = ['api_keys', 'jwt_secrets', 'email_credentials']
        
        for data_type in sensitive_data.keys():
            if data_type in critical_types:
                return 'Critical'
            elif data_type in high_types:
                return 'High'
        
        return 'Medium'
    
    def test_error_message_disclosure(self, base_url):
        """Test for information disclosure through error messages"""
        self.print_status("[*] Testing error message disclosure", "INFO")
        
        # Test various error conditions
        error_tests = [
            ('/api/nonexistent', 'GET'),
            ('/api/user/999999999', 'GET'),
            ('/api/auth/login', 'POST'),  # Without required data
            ('/api/admin/users', 'GET'),  # Likely protected endpoint
            ('/api/internal/debug', 'GET')  # Internal endpoint
        ]
        
        for endpoint, method in error_tests:
            url = urljoin(base_url, endpoint)
            
            try:
                if method == 'GET':
                    response = self.session.get(url, timeout=10)
                else:
                    response = self.session.post(url, json={}, timeout=10)
                
                # Check for verbose error messages
                if response.status_code >= 400:
                    content = response.text.lower()
                    
                    # Look for stack traces or detailed error info
                    error_indicators = [
                        'stack trace', 'traceback', 'exception',
                        'file not found', 'no such file', 'permission denied',
                        'internal server error', 'database error',
                        'mysql', 'postgresql', 'mongodb', 'redis',
                        'node_modules', 'src/', 'lib/', 'app/',
                        'line ', 'at ', 'in /'
                    ]
                    
                    found_indicators = [indicator for indicator in error_indicators if indicator in content]
                    
                    if found_indicators and len(response.text) > 200:
                        vulnerability = {
                            'type': 'Information Disclosure - Verbose Error Messages',
                            'url': url,
                            'method': method,
                            'status_code': response.status_code,
                            'error_indicators': found_indicators,
                            'severity': 'Medium',
                            'content_preview': response.text[:300] + '...'
                        }
                        
                        self.vulnerabilities.append(vulnerability)
                        self.print_status(f"[!] Verbose error found: {endpoint}", "WARNING")
                        
            except requests.exceptions.RequestException:
                continue
    
    def test_debug_endpoints(self, base_url):
        """Test for accessible debug and configuration endpoints"""
        self.print_status("[*] Testing debug and configuration endpoints", "INFO")
        
        for endpoint in self.info_endpoints:
            url = urljoin(base_url, endpoint)
            
            try:
                response = self.session.get(url, timeout=10)
                
                # Check for successful responses with potentially sensitive content
                if response.status_code == 200:
                    content_type = response.headers.get('Content-Type', '').lower()
                    
                    # Skip HTML responses that are likely just the main app
                    if 'text/html' in content_type and 'tinder | dating' in response.text.lower():
                        continue
                    
                    # Check for JSON configuration or debug info
                    if 'application/json' in content_type:
                        try:
                            json_data = response.json()
                            
                            # Look for configuration or debug information
                            sensitive_keys = [
                                'config', 'configuration', 'settings', 'env', 'environment',
                                'debug', 'version', 'build', 'database', 'redis', 'mongodb',
                                'api_key', 'secret', 'token', 'password', 'credential'
                            ]
                            
                            found_keys = []
                            for key in sensitive_keys:
                                if key in str(json_data).lower():
                                    found_keys.append(key)
                            
                            if found_keys:
                                vulnerability = {
                                    'type': 'Information Disclosure - Debug/Config Endpoint',
                                    'url': url,
                                    'status_code': response.status_code,
                                    'sensitive_keys': found_keys,
                                    'severity': 'Medium',
                                    'response_preview': str(json_data)[:300] + '...'
                                }
                                
                                self.vulnerabilities.append(vulnerability)
                                self.print_status(f"[!] Debug endpoint found: {endpoint}", "WARNING")
                                
                        except json.JSONDecodeError:
                            pass
                    
                    # Also check for sensitive patterns in any successful response
                    self.analyze_response_for_sensitive_info(response, url)
                    
            except requests.exceptions.RequestException:
                continue
    
    def test_source_code_disclosure(self, base_url):
        """Test for source code or configuration file disclosure"""
        self.print_status("[*] Testing source code disclosure", "INFO")
        
        source_files = [
            '/.env',
            '/config.json',
            '/package.json',
            '/composer.json',
            '/web.config',
            '/.git/config',
            '/webpack.config.js',
            '/tsconfig.json',
            '/.gitignore',
            '/README.md',
            '/CHANGELOG.md'
        ]
        
        for file_path in source_files:
            url = urljoin(base_url, file_path)
            
            try:
                response = self.session.get(url, timeout=10)
                
                if response.status_code == 200 and len(response.content) > 50:
                    # Check if it's not just the standard HTML page
                    if 'tinder | dating' not in response.text.lower():
                        vulnerability = {
                            'type': 'Information Disclosure - Source Code/Config File',
                            'url': url,
                            'file_type': file_path.split('.')[-1] if '.' in file_path else 'unknown',
                            'status_code': response.status_code,
                            'content_length': len(response.content),
                            'severity': 'Medium',
                            'content_preview': response.text[:200] + '...'
                        }
                        
                        self.vulnerabilities.append(vulnerability)
                        self.print_status(f"[!] Source file disclosed: {file_path}", "WARNING")
                        
                        # Also analyze for sensitive patterns
                        self.analyze_response_for_sensitive_info(response, url)
                        
            except requests.exceptions.RequestException:
                continue
    
    def run_info_disclosure_tests(self):
        """Run comprehensive information disclosure tests"""
        self.print_status("[*] Starting Tinder information disclosure tests...", "INFO")
        
        for base_url in self.base_urls:
            self.print_status(f"[*] Testing {base_url}", "INFO")
            
            # Test error message disclosure
            self.test_error_message_disclosure(base_url)
            
            # Test debug endpoints
            self.test_debug_endpoints(base_url)
            
            # Test source code disclosure
            self.test_source_code_disclosure(base_url)
        
        self.generate_report()
    
    def generate_report(self):
        """Generate information disclosure vulnerability report"""
        if not self.vulnerabilities:
            self.print_status("[*] No information disclosure vulnerabilities found.", "INFO")
            return
        
        self.print_status(f"\n[!] Found {len(self.vulnerabilities)} information disclosure vulnerabilities:", "ERROR")
        
        report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'target_urls': self.base_urls,
            'vulnerabilities_found': len(self.vulnerabilities),
            'vulnerabilities': self.vulnerabilities
        }
        
        # Save detailed report
        with open('tinder_info_disclosure_vulnerabilities.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        # Print summary
        severity_counts = {'Critical': 0, 'High': 0, 'Medium': 0, 'Low': 0}
        
        for vuln in self.vulnerabilities:
            severity_counts[vuln.get('severity', 'Medium')] += 1
        
        print(f"\n--- Information Disclosure Summary ---")
        for severity, count in severity_counts.items():
            if count > 0:
                print(f"{severity}: {count}")
        
        print(f"\n--- Detailed Findings ---")
        for i, vuln in enumerate(self.vulnerabilities, 1):
            print(f"\n{i}. {vuln['type']} ({vuln['severity']})")
            print(f"   URL: {vuln['url']}")
            
            if 'sensitive_data' in vuln:
                print(f"   Sensitive Data Types: {list(vuln['sensitive_data'].keys())}")
            
            if 'error_indicators' in vuln:
                print(f"   Error Indicators: {vuln['error_indicators']}")
        
        self.print_status(f"\n[+] Detailed report saved to tinder_info_disclosure_vulnerabilities.json", "SUCCESS")

def main():
    if len(sys.argv) < 2:
        print("Usage: python tinder_info_disclosure_tester.py <url1> [url2] [url3]...")
        print("Example: python tinder_info_disclosure_tester.py https://www.tinder.com https://staging.tinder.com")
        sys.exit(1)
    
    urls = sys.argv[1:]
    tester = TinderInfoDisclosureTester(urls)
    tester.run_info_disclosure_tests()

if __name__ == "__main__":
    main()
