<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="warn" shutdownHook="disable">
  <Properties>
    <Property name="dfltPattern">%d{yyyy-MM-dd'T'HH:mm:ss.SSS} %-5level [%t] %logger: %msg%n</Property>
  </Properties>
  <Appenders>
    <Console name="STDERR" target="SYSTEM_ERR">
      <PatternLayout pattern="${dfltPattern}"/>
    </Console>
  </Appenders>
  <Loggers>
    <Logger name="com.netflix.spectator" level="info"/>
    <Root level="info">
      <AppenderRef ref="STDERR"/>
    </Root>
  </Loggers>
</Configuration>
