[0m[[0m[0mdebug[0m] [0m[0mPackaging C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\atlas-lwcapi_2.13-1.8.0-SNAPSHOT.jar ...[0m
[0m[[0m[0mdebug[0m] [0m[0mInput file mappings:[0m
[0m[[0m[0mdebug[0m] [0m[0m	com[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\ApiSettings$$anonfun$$lessinit$greater$1.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\ApiSettings$$anonfun$$lessinit$greater$1.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\ApiSettings$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\ApiSettings$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\ApiSettings.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\ApiSettings.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\EvaluateApi$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\EvaluateApi$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\EvaluateApi$EvaluateRequest$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\EvaluateApi$EvaluateRequest$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\EvaluateApi$EvaluateRequest.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\EvaluateApi$EvaluateRequest.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\EvaluateApi$Item$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\EvaluateApi$Item$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\EvaluateApi$Item.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\EvaluateApi$Item.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\EvaluateApi.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\EvaluateApi.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\ExpressionApi$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\ExpressionApi$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\ExpressionApi$CacheEntry.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\ExpressionApi$CacheEntry.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\ExpressionApi$EncodedExpressions$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\ExpressionApi$EncodedExpressions$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\ExpressionApi$EncodedExpressions.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\ExpressionApi$EncodedExpressions.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\ExpressionApi$ExpressionsCache.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\ExpressionApi$ExpressionsCache.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\ExpressionApi$Return$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\ExpressionApi$Return$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\ExpressionApi$Return.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\ExpressionApi$Return.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\ExpressionApi.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\ExpressionApi.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\ExpressionMetadata$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\ExpressionMetadata$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\ExpressionMetadata.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\ExpressionMetadata.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\ExpressionSplitter$$anonfun$$nestedInanonfun$parse$1$1.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\ExpressionSplitter$$anonfun$$nestedInanonfun$parse$1$1.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\ExpressionSplitter$$anonfun$$nestedInanonfun$parse$1$2.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\ExpressionSplitter$$anonfun$$nestedInanonfun$parse$1$2.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\ExpressionSplitter$$anonfun$$nestedInanonfun$parse$1$3.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\ExpressionSplitter$$anonfun$$nestedInanonfun$parse$1$3.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\ExpressionSplitter$$anonfun$1.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\ExpressionSplitter$$anonfun$1.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\ExpressionSplitter$$anonfun$2.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\ExpressionSplitter$$anonfun$2.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\ExpressionSplitter$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\ExpressionSplitter$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\ExpressionSplitter$DataExprMeta$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\ExpressionSplitter$DataExprMeta$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\ExpressionSplitter$DataExprMeta.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\ExpressionSplitter$DataExprMeta.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\ExpressionSplitter.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\ExpressionSplitter.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\package$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\package$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\package.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\package.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\QueueHandler.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\QueueHandler.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\StartupDelayService.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\StartupDelayService.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\StreamMetadata$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\StreamMetadata$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\StreamMetadata.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\StreamMetadata.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\StreamsApi.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\StreamsApi.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\StreamSubscriptionManager.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\StreamSubscriptionManager.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\SubscribeApi$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\SubscribeApi$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\SubscribeApi$ErrorMsg$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\SubscribeApi$ErrorMsg$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\SubscribeApi$ErrorMsg.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\SubscribeApi$ErrorMsg.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\SubscribeApi$Errors$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\SubscribeApi$Errors$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\SubscribeApi$Errors.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\SubscribeApi$Errors.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\SubscribeApi$SubscribeRequest$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\SubscribeApi$SubscribeRequest$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\SubscribeApi$SubscribeRequest.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\SubscribeApi$SubscribeRequest.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\SubscribeApi.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\SubscribeApi.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\Subscription$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\Subscription$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\Subscription.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\Subscription.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\SubscriptionManager$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\SubscriptionManager$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\SubscriptionManager$ConcurrentSet.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\SubscriptionManager$ConcurrentSet.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\SubscriptionManager$StreamInfo$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\SubscriptionManager$StreamInfo$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\SubscriptionManager$StreamInfo.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\SubscriptionManager$StreamInfo.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\SubscriptionManager$StreamSummary$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\SubscriptionManager$StreamSummary$.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\SubscriptionManager$StreamSummary.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\SubscriptionManager$StreamSummary.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\SubscriptionManager.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\SubscriptionManager.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\WebSocketSessionManager$$anon$1.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\WebSocketSessionManager$$anon$1.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\WebSocketSessionManager.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\WebSocketSessionManager.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	reference.conf[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\classes\reference.conf[0m
[0m[[0m[0mdebug[0m] [0m[0mDone packaging.[0m
