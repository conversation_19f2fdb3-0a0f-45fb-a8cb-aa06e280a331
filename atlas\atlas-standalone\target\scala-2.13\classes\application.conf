atlas {
  pekko {
    // These are overridden because the standalone pulls in both webapi and lwc modules.
    // The user can run the one that is needed for a given test by using one of the specific
    // profile configurations. The default application config here ignores the setup done
    // by the reference confs.
    actors = [
      {
        name = "deadLetterStats"
        class = "com.netflix.atlas.pekko.DeadLetterStatsActor"
      }
    ]

    api-endpoints = [
      "com.netflix.atlas.pekko.HealthcheckApi",
      "com.netflix.atlas.pekko.ConfigApi",
      "com.netflix.atlas.pekko.StaticPages"
    ]
  }
}

netflix.iep.atlas {
  enabled = false
}
