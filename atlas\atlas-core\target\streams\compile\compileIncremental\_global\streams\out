[0m[[0m[0mdebug[0m] [0m[0m[zinc] IncrementalCompile -----------[0m
[0m[[0m[0mdebug[0m] [0m[0mIncrementalCompile.incrementalCompile[0m
[0m[[0m[0mdebug[0m] [0m[0mprevious = Stamps for: 836 products, 138 sources, 7 libraries[0m
[0m[[0m[0mdebug[0m] [0m[0mcurrent source = Set(${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/FilterExpr.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/TimeSeriesExpr.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/norm/UpdateValueFunction.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/Interner.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/TimeSeq.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/DoubleIntHashMap.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/ModelExtractors.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/EvalContext.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/ByteBufferInputStream.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/TagKey.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/norm/SumValueFunction.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/package.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/ConsolidationFunction.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/algorithm/OnlineRollingMax.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/validation/KeyLengthRule.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/StyleVocabulary.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/validation/HasKeyRule.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/TraceVocabulary.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/stacklang/Vocabulary.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/MathExpr.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/IdMap.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/index/RoaringTagIndex.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/StatefulVocabulary.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/algorithm/OnlineDelay.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/norm/NormalizationCache.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/ListHelper.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/PrimeFinder.java, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/MathVocabulary.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/FilterVocabulary.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/algorithm/OnlineDerivative.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/db/TimeSeriesBuffer.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/Tag.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/index/TaggedItemIndex.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/stacklang/Context.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/stacklang/Interpreter.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/validation/ValidationResult.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/ComparableComparator.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/TaggedItem.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/QueryVocabulary.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/CustomVocabulary.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/UnitPrefix.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/IsoDateTimeParser.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/validation/ValueLengthRule.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/index/CachingTagIndex.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/algorithm/OnlineSlidingDes.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/ItemId.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/db/BlockStoreItem.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/EventVocabulary.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/algorithm/OnlineIgnoreN.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/Block.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/algorithm/OnlineAlgorithm.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/algorithm/OnlineRollingMean.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/DefaultSettings.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/norm/DedupValueFunction.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/index/package.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/norm/ValueFunction.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/IntIntHashMap.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/algorithm/OnlineTrend.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/RefDoubleHashMap.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/index/LazyOrBitmap.java, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/algorithm/OnlineRollingSum.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/norm/NormalizeValueFunction.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/DatapointTuple.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/CollectorStats.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/DataExpr.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/validation/TagRule.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/db/AggregateCollector.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/db/StaticDatabase.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/InternMap.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/db/MemoryDatabase.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/StatefulExpr.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/stacklang/Extractors.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/algorithm/OnlineIntegral.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/stacklang/StackItem.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/norm/RateValueFunction.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/validation/CompositeTagRule.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/DataVocabulary.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/ArrayHelper.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/Math.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/TraceQuery.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/Features.java, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/db/Database.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/Datapoint.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/IntHashSet.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/validation/MaxUserTagsRule.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/index/IndexStats.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/stacklang/Word.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/BoundedPriorityBuffer.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/norm/package.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/db/Limits.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/CharBufferReader.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/StyleExpr.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/TimeSeries.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/IdentityMap.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/algorithm/RollingBuffer.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/ResultSet.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/FastGzipOutputStream.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/Query.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/EventExpr.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/Step.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/algorithm/Pipeline.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/Expr.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/algorithm/OnlineRollingMin.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/Shards.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/Strings.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/ItemIdCalculator.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/RefIntHashMap.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/Hash.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/SortedTagMap.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/validation/ValidCharactersRule.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/index/BatchUpdateTagIndex.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/validation/KeyPatternRule.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/norm/MaxValueFunction.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/stacklang/StandardVocabulary.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/db/SimpleStaticDatabase.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/Streams.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/IntRefHashMap.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/norm/RollingValueFunction.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/BlockStats.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/algorithm/OnlineDes.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/db/BlockStore.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/db/DataSet.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/SummaryStats.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/LongIntHashMap.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/validation/ReservedKeyRule.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/index/SimpleTagIndex.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/LongHashSet.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/RollingInterval.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/validation/NameValueLengthRule.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/algorithm/OnlineRollingCount.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/algorithm/AlgoState.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/validation/ValuePatternRule.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/util/TimeWave.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/algorithm/RollingSumBuffer.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/validation/Rule.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/index/TagIndex.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/index/TagQuery.scala, ${BASE}/atlas-core/src/main/scala/com/netflix/atlas/core/model/DsType.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m> initialChanges = InitialChanges(Changes(added = Set(), removed = Set(), changed = Set(), unmodified = ...),Set(),Set(),API Changes: Set())[0m
[0m[[0m[0mdebug[0m] [0m[0mNo changes[0m
