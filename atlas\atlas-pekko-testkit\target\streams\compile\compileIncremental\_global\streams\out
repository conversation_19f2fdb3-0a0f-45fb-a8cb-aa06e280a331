[0m[[0m[0mdebug[0m] [0m[0m[zinc] IncrementalCompile -----------[0m
[0m[[0m[0mdebug[0m] [0m[0mIncrementalCompile.incrementalCompile[0m
[0m[[0m[0mdebug[0m] [0m[0mprevious = Stamps for: 0 products, 0 sources, 0 libraries[0m
[0m[[0m[0mdebug[0m] [0m[0mcurrent source = Set(${BASE}/atlas-pekko-testkit/src/main/scala/com/netflix/atlas/pekko/testkit/MUnitRouteSuite.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m> initialChanges = InitialChanges(Changes(added = Set(${BASE}/atlas-pekko-testkit/src/main/scala/com/netflix/atlas/pekko/testkit/MUnitRouteSuite.scala), removed = Set(), changed = Set(), unmodified = ...),Set(),Set(),API Changes: Set())[0m
[0m[[0m[0mdebug[0m] [0m[0mFull compilation, no sources in previous analysis.[0m
[0m[[0m[0mdebug[0m] [0m[0mall 1 sources are invalidated[0m
[0m[[0m[0mdebug[0m] [0m[0mInitial set of included nodes: [0m
[0m[[0m[0mdebug[0m] [0m[0mRecompiling all sources: number of invalidated sources > 50.0 percent of all sources[0m
[0m[[0m[0mdebug[0m] [0m[0mcompilation cycle 1[0m
[0m[[0m[0minfo[0m] [0m[0mcompiling 1 Scala source to C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-pekko-testkit\target\scala-2.13\classes ...[0m
[0m[[0m[0mdebug[0m] [0m[0mReturning already retrieved and compiled bridge: C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala2-sbt-bridge\2.13.16\scala2-sbt-bridge-2.13.16.jar.[0m
[0m[[0m[31merror[0m] [0m[0m'17' is not a valid choice for '-release'[0m
[0m[[0m[31merror[0m] [0m[0mbad option: '-release'[0m
[0m[[0m[0mdebug[0m] [0m[0mCompilation failed (CompilerInterface)[0m
[0m[[0m[31merror[0m] [0m[0m(atlas-pekko-testkit / Compile / [31mcompileIncremental[0m) Compilation failed[0m
