["sbt.Task[scala.collection.Seq[java.nio.file.Path]]", ["C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$Or$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\index\\TagQuery$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\norm\\NormalizationCache$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$LessThanEqual$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\SimpleAggregateCollector.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DatapointTuple.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$GreaterThanEqual$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$ClampMin$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$Palette$$anonfun$matcher$4.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\SummaryStats.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$Stat.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$True$$anonfun$matcher$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$RollingMax$$anonfun$matcher$4.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterVocabulary$PriorityK.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$StatLast$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Swap$$anonfun$executor$13.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\ValidationResult$Fail$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceVocabulary.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$StatMin$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$RollingSum$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Map$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ResultSet$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\index\\TaggedItemIndex.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterVocabulary$Stat$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$SampleCount$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineRollingMax$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$Delay$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\EventVocabulary$TableWord$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$Sum$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$StripStyle$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$NamedRewrite$$anonfun$executor$10.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TimeSeries.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$Not$$anonfun$matcher$8.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Time$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$CfWord$$anonfun$matcher$4.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ConstantBlock.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\Shards.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Subtract$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\IntRefHashMap.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$PerStep$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$RollingMean$$anonfun$matcher$5.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\IdMap.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$ClampMin$$anonfun$executor$11.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\OffsetTimeSeq.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\SimpleWord.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\BasicTimeSeries.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\IsoDateTimeParser$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterVocabulary$Stat$$anonfun$executor$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Max.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\Math$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$BinaryWord.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TaggedItem.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$As$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceVocabulary$SpanTimeSeriesWord$$anonfun$matcher$5.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\ValuePatternRule$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\RefDoubleHashMap.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Power$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TimeSeriesExpr.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$LessThanEqual$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\BlockStoreItem.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$As$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\EventExpr$Sample.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$RollingMin$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineDerivative$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceQuery$SpanOr$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$StatComparator$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$BottomKOthersMin$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$RollingMean$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$Delay.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$FAdd$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ModelExtractors$DoubleListType$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Sqrt.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\ArrayHelper.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ConsolidationFunction$Min$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Datapoint.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\Shards$Group$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$CfWord$$anonfun$executor$4$$anonfun$2.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\PrimeFinder.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\index\\BatchUpdateTagIndex$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\EvalContext.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\BoundedPriorityBuffer.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Min$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$StatCount$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$StatAvg$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$GreaterThan$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$FSubtract$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataExpr$Min$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$SearchAndReplace$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineDerivative.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\TimeSeriesBuffer.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$Regex$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\Interner.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\EventVocabulary$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Sum$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ItemIdCalculator$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$Delay$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$PriorityFilterExpr.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TimeSeries$Aggregator.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Or.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterVocabulary$Filter$$anonfun$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$RollingSum$$anonfun$executor$6.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\RollupBlock$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\MinAggregateCollector.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$In.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterVocabulary$PriorityK$$anonfun$matcher$4.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\UnaryOpTimeSeq.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\norm\\package.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\NameValueLengthRule$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$TimeSeriesSummary.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$StripStyle$$anonfun$executor$8.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$CfWord$$anonfun$executor$4$$anonfun$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$LessThan$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleExpr.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Clear$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$CfAvg$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\MemoryDatabase$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$Not$$anonfun$executor$8.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineIgnoreN.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterVocabulary$StatMax$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$StyleWord$$anonfun$executor$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$Stat$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$OnlineExpr.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$HasKey$$anonfun$executor$3.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$TopKOthersSum.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\UnitPrefix$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Negate$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$Integral.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterVocabulary$StatCount$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Roll$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\Extractors$IntType$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$Color$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ModelExtractors$TraceQueryType$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Sqrt$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\LazyTaggedItem.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$GroupBy$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\BasicTimeSeries$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterVocabulary$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$Des.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$GroupBy$$anonfun$executor$2.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceQuery$Simple$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Divide$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$RollingMax$$anonfun$executor$4.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\norm\\package$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\StringInterner.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceQuery$Simple.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\AlgoState.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Or$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\Hash.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataExpr$Count$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\CompressedArrayBlock$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Add.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\CustomVocabulary.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$PatternQuery.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$LessThanEqual.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Sqrt$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$UnaryWord$$anonfun$matcher$13.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$RollingCount$$anonfun$matcher$2.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$Trend.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$SlidingDes$$anonfun$matcher$8.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$BottomKOthersSum.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$Filter$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineIntegral$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\CollectorStats.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\Rule$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\Interpreter$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\ByteBufferInputStream.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\LongIntHashMap$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ModelExtractors$EventExprType$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Const$$anonfun$matcher$3.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$TopKOthersAvg.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ModelExtractors$DurationType$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\TimeWave$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterVocabulary$StatTotal$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\IdMap$$anon$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$GroupBy$$anonfun$matcher$2.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ModelExtractors$DataExprType$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$NamedRewrite$$anonfun$matcher$10.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\Interpreter$IsWord$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Add$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$LessThan$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$RollingMean$$anonfun$executor$5.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$Integral$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$FDivide$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceQuery.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$Legend$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceVocabulary$ChildWord$$anonfun$executor$3.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\EventVocabulary$TableWord$$anonfun$executor$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Over$$anonfun$executor$9.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$And$$anonfun$matcher$6.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$TimeSeriesSummary$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$BottomKOthersMax$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\index\\BatchUpdateTagIndex.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\RefIntHashMap.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MapStepTimeSeq.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\KeyPatternRule.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Max$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$KeyValueWord$$anonfun$executor$4.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$Integral$$anonfun$matcher$10.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\norm\\NormalizationCache.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$LessThan.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$LessThan$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceVocabulary$SpanFilterWord$$anonfun$matcher$4.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$ReverseRot$$anonfun$matcher$12.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Freeze$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\norm\\DedupValueFunction.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\ArrayHelper$Merger.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$Alpha$$anonfun$executor$2.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$GroupBy.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Drop$$anonfun$executor$3.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleExpr$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$GreaterThanEqual$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\CollectorStats$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$SeededRandom$$anonfun$matcher$6.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\DoubleIntHashMap$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterVocabulary$StatMin$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterVocabulary$Stat$$anonfun$matcher$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Drop$$anonfun$matcher$3.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$Not$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$And$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineRollingMean.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DsType$Rate$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\DataSet$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\norm\\NormalizationCache$$anon$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Min$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FunctionTimeSeq.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\RollupBlock.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineRollingMin.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Abs$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineDes$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\EventVocabulary$SampleWord$$anonfun$executor$2.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\index\\CachingTagIndex$$anon$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$FDivide$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\IsoDateTimeParser.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Depth$$anonfun$executor$2.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$CommonGroupBy$$anonfun$matcher$8.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ArrayBlock.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\CompositeTagRule.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\CustomVocabulary$CustomAvg.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineDelay$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$Order$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\Interpreter$WordToken.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$LessThanEqual$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\NameValueLengthRule.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Rot$$anonfun$matcher$11.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Time$$anonfun$executor$7.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$Des$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$Des$$anonfun$matcher$7.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TimeSeries$SimpleAggregator.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\index\\package$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\Shards$Mapper.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\Hash$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\NoopInterner.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$CommonQuery$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Pick$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ConsolidationFunction$Max$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\IntHashSet$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ItemId.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$TopK$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\MemoryBlockStore.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\BlockStore.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Macro.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\index\\TaggedItemIndex$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$CommonQuery$$anonfun$executor$9.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$Trend$$anonfun$executor$9.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\OpenHashInternMap.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$SeededRandom$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$LineStyle$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterVocabulary$StatLast$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$AggrWord$$anonfun$matcher$15.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Datapoint$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Sine.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\IdMap$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\KeyLengthRule.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Negate$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\RollingSumBuffer.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\EventExpr.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$FDivide.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Constant.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$NamedRewrite$$anonfun$applyGroupBy$1$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\ValidationResult$Pass$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\index\\IndexStats$ValueEntry$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$ClampMin.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\BasicTaggedItem$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Max$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineDelay.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\DoubleIntHashMap.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DsType$Gauge$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$GroupBy$$anonfun$executor$2$$anonfun$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$Ends$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\RefIntHashMap$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$Offset$$anonfun$matcher$5.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$As$$anonfun$executor$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\RollingBuffer.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterVocabulary$StatWord$$anonfun$matcher$2.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$ClampMax$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$RollingMax.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\EventExpr$Raw$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$Trend$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$Starts$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\ReservedKeyRule.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\index\\TagIndex.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$Des$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\IntIntHashMap$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$RollingMax$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$Not$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\CustomVocabulary$CustomAvg$$anonfun$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\Strings.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$Offset$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\CustomVocabulary$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$CfWord.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterVocabulary$StatWord.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$Equal$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$KeyQuery.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$BottomKOthersAvg.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$CfSum$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$BottomKOthersAvg$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\Shards$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Const$$anonfun$executor$3.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$RollingCount$$anonfun$executor$2.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$Delay$$anonfun$executor$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Percentiles$$anonfun$matcher$16.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$Palette$$anonfun$executor$4.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\CompositeTagRule$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Subtract.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Count$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\IntHashSet.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineSlidingDes$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$Or$$anonfun$executor$7.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$Regex.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Tag.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$Not.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$RollingMin$$anonfun$executor$3.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$FMultiply.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$FSubtract.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterVocabulary$StatAvg$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TaggedItem$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceVocabulary$SpanAndWord$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$ClampMin$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$FMultiply$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceQuery$SpanTimeSeries.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$As$$anonfun$matcher$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ModelExtractors$AggrType$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\Shards$Group.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$SlidingDes$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$Contains$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\ConcurrentInternMap.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\index\\IndexStats$KeyStat$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\SortedTagMap$Builder.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Multiply$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$NDrop$$anonfun$executor$7.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\index\\IndexStats.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\EventExpr$Table$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\KeyLengthRule$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$RollingSum.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\AlgoState$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$Limit$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$LessThan.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineIntegral.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$RollingMin$$anonfun$matcher$3.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\norm\\SumValueFunction.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\norm\\RollingValueFunction.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\EvalContext$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$Equal.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\Math.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\Interpreter$WordToken$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$LessThanEqual.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Negate.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$SearchAndReplace$$anonfun$matcher$7.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Drop$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\MaxUserTagsRule$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\CollectorStatsBuilder.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\Interner$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\SortedTagMap.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\ListHelper$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\ComparableComparator.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\index\\MutableTagIndex.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$False$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$CommonGroupBy$$anonfun$executor$8.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TagKey.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DsType$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$CommonQuery$$anonfun$matcher$9.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Over$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\index\\SimpleTagIndex.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$KeyValueWord$$anonfun$matcher$4.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$LessThan$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Percentiles$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Time.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\CustomVocabulary$CustomAvg$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\RollingBuffer$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\PredefinedInterner.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ConsolidationFunction.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$Color$$anonfun$executor$3.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\EventExpr$Raw.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\ValidationResult$Fail.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DefaultSettings.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$RollingMean$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataExpr$Max.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\RollingInterval$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$In$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$RegexIgnoreCase.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataExpr$Max$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Pi$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Call$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$Derivative$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$ClampMax$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\index\\IndexStats$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Count$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$RegexIgnoreCase$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Roll$$anonfun$executor$10.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$GreaterThanEqual$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\Streams$$anon$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\MaxAggregateCollector.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Abs$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceQuery$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceQuery$Child.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataExpr$GroupBy.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$SlidingDes$$anonfun$executor$8.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\package$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\Strings$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceVocabulary$SpanAndWord$$anonfun$executor$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$CfMax$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineRollingSum$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$SeededRandom$$anonfun$executor$6.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Time$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$DataWord$$anonfun$executor$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$CommonGroupBy$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TimeSeries$NoopAggregator$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MutableBlock.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\IdentityMap.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$BottomKOthersMax.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\BinaryOpTimeSeq.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceVocabulary$SpanOrWord$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$Filter$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\index\\package.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceVocabulary$SpanOrWord$$anonfun$executor$2.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FloatArrayBlock$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$BinaryWord$$anonfun$matcher$14.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\Limits$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$UnaryWord$$anonfun$executor$13.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataExpr$Sum.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$Or$$anonfun$matcher$7.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$As.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\index\\TagQuery.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Or$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$And$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ConsolidationFunction$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$GroupBy$$anonfun$matcher$2.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DatapointTuple$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$Or$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Pi$$anonfun$executor$4.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Percentiles.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$BottomK$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineRollingMin$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$NDrop$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\MemoryDatabase$RebuildTask.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineDes.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$Derivative$$anonfun$matcher$11.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ItemIdCalculator.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\SimpleStaticDatabase.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceQuery$SpanFilter.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\CustomVocabulary$CustomAvg$$anonfun$matcher$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\LazyTimeSeries.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$Delay$$anonfun$matcher$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Depth$$anonfun$matcher$2.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$TimeSpan$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\IdentityMap$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$NList$$anonfun$matcher$8.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\norm\\RateValueFunction.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$RegexIgnoreCase$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$StripStyle$$anonfun$matcher$8.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\EventVocabulary$SampleWord$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$RollingCount.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceVocabulary$ChildWord$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ArrayTimeSeq.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Multiply$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Swap$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Count.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$True$$anonfun$executor$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DsType.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\norm\\ValueFunction.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\Pipeline$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$RollingMax$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\BlockStats$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$Des$$anonfun$executor$7.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\LongIntHashMap.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\SparseBlock.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\Step$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataExpr$Sum$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ConstantBlock$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Format$$anonfun$executor$5.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\MemoryBlockStore$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$SlidingDes$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$Integral$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceVocabulary$SpanTimeSeriesWord$$anonfun$executor$5.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Percentiles$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\GroupByAggregateCollector.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Swap$$anonfun$matcher$13.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineIgnoreN$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\ValidCharactersRule.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$StatMax$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Random$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$ClampMax$$anonfun$matcher$12.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineRollingSum.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$Palette$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\KeyPatternRule$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$TopKOthersMax.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$StyleWord$$anonfun$matcher$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$False$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ArrayBlock$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\Shards$ReplicaMapper.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\index\\TaggedItemIndex$Builder.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\AllAggregateCollector.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\Pipeline.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$True$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\ValueLengthRule.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$Equal$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterVocabulary$Filter$$anonfun$matcher$3.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$GreaterThanEqual.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$ReverseRot$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$Color$$anonfun$matcher$3.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineRollingMax.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$RollingSum$$anonfun$matcher$6.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\Context.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\Interpreter$Step$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\MaxUserTagsRule.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\TagRule$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$Or.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\AggregateCollector$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineAlgorithm$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceQuery$SpanAnd.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$UnaryWord.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$NList$$anonfun$executor$8.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Time$$anonfun$matcher$7.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterVocabulary$Filter$$anonfun$executor$3.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\Database.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ResultSet.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\InternMap.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$ReverseRot$$anonfun$executor$12.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$BinaryMathExpr.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Constant$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\LongHashSet.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\index\\RoaringTagIndex.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$CfMin$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$Derivative.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$True$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$NamedRewrite$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\UnitPrefix.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$TopKOthersMin$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Each$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$Offset$$anonfun$executor$5.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$RollingCount$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Abs.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineTrend.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$Decode$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Tag$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineRollingCount$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\CompressedArrayBlock.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$TopK.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$False$$anonfun$matcher$2.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\StaticDatabase$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Sine$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$UnaryMathExpr.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineSlidingDes.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Set$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$HasKey$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\norm\\MaxValueFunction.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\Vocabulary.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TagKey$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Depth$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$GreaterThan$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\package.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$NDrop$$anonfun$matcher$7.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$Filter.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ModelExtractors$StringListType$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceVocabulary$SpanOrWord$$anonfun$matcher$2.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$GreaterThanEqual$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\index\\CachingTagIndex.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$Decode$$anonfun$executor$6.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceQuery$SpanTimeSeries$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$BottomK.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$Trend$$anonfun$matcher$9.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\EventVocabulary.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$LineWidth$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\ValuePatternRule.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\DataSet.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ConsolidationFunction$SumOrAvgCf.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Block.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\EventExpr$Table.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$RollingMean.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Sine$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\Streams$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\BlockStats.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Format$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$AggrWord$$anonfun$executor$15.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Add$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$Sort$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Block$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$TopKOthersMax$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$SlidingDes.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StackItem.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$TimeSpan.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$LessThanEqual$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$GreaterThan.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Percentiles$$anonfun$executor$16.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\Extractors.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$Trend$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$Alpha$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$Offset$$anonfun$executor$3.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\IntRefHashMap$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\index\\IndexStats$KeyStat.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$ClampMax.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Random$$anonfun$executor$5.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$Count$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Expr.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\RollingInterval.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\index\\LazyOrBitmap.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\IntIntHashMap.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$Derivative$$anonfun$executor$11.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterVocabulary$PriorityK$$anonfun$executor$4.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TimeSeries$CountAggregator.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceQuery$Child$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$Decode$$anonfun$matcher$6.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataExpr$AggregateFunction.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$Axis$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\Word.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\Interpreter.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineRollingCount.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Sum$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceQuery$SpanAnd$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\Context$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\Features.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterVocabulary.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$Derivative$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\norm\\NormalizeValueFunction$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Pick$$anonfun$executor$6.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\EventVocabulary$SampleWord$$anonfun$matcher$2.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceVocabulary$SpanTimeSeriesWord$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\TagRule.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\TimeSeriesBuffer$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\OpenHashInternMap$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$AggrWord.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Min.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\HasKeyRule$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Const$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Rot$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\index\\RoaringTagIndex$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Power.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$FMultiply$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\ListHelper.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$And$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$GreaterThan$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$ClampMin$$anonfun$matcher$11.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\StaticDatabase.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$RollingMin$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$CommonQuery$$anonfun$executor$9$$anonfun$3.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\ValueLengthRule$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$All$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataExpr$Consolidation.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineTrend$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Pi$$anonfun$matcher$4.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataExpr$Count.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Divide$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\EventExpr$Sample$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$RollingCount$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$FAdd$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Clear$$anonfun$executor$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\index\\IndexStats$ValueEntry.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$Max$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceVocabulary$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$FAdd.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\ValidCharactersRule$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\TimeWave.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Clear$$anonfun$matcher$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Divide.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TimeSeries$AvgAggregator.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\CharBufferReader.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\norm\\NormalizationCache$CacheValue.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\EventExpr$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$GreaterThan.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataExpr$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\norm\\UpdateValueFunction.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$GroupBy$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$GreaterThanEqual.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\FastGzipOutputStream.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Random$$anonfun$matcher$5.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterVocabulary$Filter$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$Alpha$$anonfun$matcher$2.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$HasKey$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Format$$anonfun$matcher$5.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$TopKOthersMin.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$TopKOthersAvg$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Roll$$anonfun$matcher$10.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\SumAggregateCollector.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataExpr$All.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceVocabulary$SpanFilterWord$$anonfun$executor$4.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataExpr.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$Integral$$anonfun$executor$10.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\SummaryStats$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Sum.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Random$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$NList$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Macro$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$NamedRewrite$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Rot$$anonfun$executor$11.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulVocabulary$RollingSum$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\Streams.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$False$$anonfun$executor$2.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\IdentityMap$$anon$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterVocabulary$StatWord$$anonfun$executor$2.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\SortedTagMap$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataExpr$GroupBy$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\ArrayHelper$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\SortedTagMap$$anon$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$Offset$$anonfun$matcher$3.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$BottomKOthersSum$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\SparseBlock$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineRollingMean$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$BinaryWord$$anonfun$executor$14.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ModelExtractors$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$GroupBy$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\Step.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$GroupBy$$anonfun$executor$2.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$And.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Multiply.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\algorithm\\OnlineAlgorithm.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$TopKOthersSum$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$ClampMax$$anonfun$executor$12.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\BlockStoreItem$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$And$$anonfun$executor$6.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$In$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$And$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterVocabulary$PriorityK$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$DataWord.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$TimeSpan$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$SearchAndReplace$$anonfun$executor$7.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataExpr$Min.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\norm\\NormalizeValueFunction.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$KeyValueWord.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\norm\\ListValueFunction.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceQuery$SpanOr.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceVocabulary$SpanAndWord$$anonfun$matcher$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\LazyTimeSeries$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TimeSeriesExpr$$anonfun$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataExpr$Consolidation$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ConsolidationFunction$Sum$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\RefDoubleHashMap$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ModelExtractors$PresentationType$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\Shards$LocalMapper.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$SeededRandom.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$Min$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\CaffeineInterner.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\LimitedAggregateCollector.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$HasKey.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$SeededRandom$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceVocabulary$ChildWord$$anonfun$matcher$3.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$In$$anonfun$matcher$5.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\MemoryDatabase.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$PerStep$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\HasKeyRule.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\ReservedKeyRule$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Over$$anonfun$matcher$9.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$And.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\BasicTaggedItem.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$StatExpr.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$CfWord$$anonfun$executor$4.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ItemId$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StatefulExpr$RollingMin.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Pick$$anonfun$matcher$6.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ModelExtractors.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Dup$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$Offset$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\LongHashSet$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$AggrMathExpr.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceVocabulary$SpanFilterWord$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\Interpreter$Step.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\InternMap$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\Extractors$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$BottomKOthersMin.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Dup$$anonfun$executor$4.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\ValidationResult.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\SortedTagMap$IdView.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\Rule.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\AggregateCollector.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$FSubtract$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataExpr$All$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FilterExpr$StatTotal$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$CommonGroupBy$$anonfun$2.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\StyleVocabulary$StyleWord.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\Extractors$DoubleType$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DataVocabulary$DataWord$$anonfun$matcher$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$Regex$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TraceQuery$SpanFilter$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ConsolidationFunction$Avg$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$In$$anonfun$executor$5.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ModelExtractors$TraceFilterType$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$HasKey$$anonfun$matcher$3.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TimeSeq.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\CustomVocabulary$CustomAvg$$anonfun$executor$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$GreaterThan$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Dup$$anonfun$matcher$4.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\validation\\ValidationResult$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\db\\Limits.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$NamedRewrite.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\util\\StringInterner$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\EventVocabulary$TableWord$$anonfun$matcher$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\Query$KeyValueQuery.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\TimeSeries$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ModelExtractors$TimeSeriesType$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$ToList$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\QueryVocabulary$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$PerStep.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\ModelExtractors$TraceTimeSeriesType$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\DefaultSettings$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\stacklang\\StandardVocabulary$Get$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathExpr$Subtract$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\FloatArrayBlock.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\core\\model\\MathVocabulary$Power$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-core\\target\\scala-2.13\\zinc\\inc_compile_2.13.zip"]]