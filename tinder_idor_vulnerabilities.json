{"timestamp": "2025-06-28 19:42:27", "target_urls": ["https://www.tinder.com", "https://staging.tinder.com"], "vulnerabilities_found": 200, "vulnerabilities": [{"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/1", "test_id": "1", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/2", "test_id": "2", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/3", "test_id": "3", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/4", "test_id": "4", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/5", "test_id": "5", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/6", "test_id": "6", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/7", "test_id": "7", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/8", "test_id": "8", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/9", "test_id": "9", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/10", "test_id": "10", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/users/1", "test_id": "1", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/users/2", "test_id": "2", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\"></title><link rel=\"preconnect\" href=\"https://images-ssl.gotinder.com\"/><link rel=\"dns-prefetch\" href=\"https://api.gotinder.com\"/><link rel=\"dns-prefetch\" href=\"https://apis.google.com\"/><link rel=...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/users/3", "test_id": "3", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/users/4", "test_id": "4", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\"></title><link rel=\"preconnect\" href=\"https://images-ssl.gotinder.com\"/><link rel=\"dns-prefetch\" href=\"https://api.gotinder.com\"/><link rel=\"dns-prefetch\" href=\"https://apis.google.com\"/><link rel=...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/users/5", "test_id": "5", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/users/6", "test_id": "6", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/users/7", "test_id": "7", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/users/8", "test_id": "8", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/users/9", "test_id": "9", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/users/10", "test_id": "10", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/profile/1", "test_id": "1", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/profile/2", "test_id": "2", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/profile/3", "test_id": "3", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/profile/4", "test_id": "4", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/profile/5", "test_id": "5", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/profile/6", "test_id": "6", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/profile/7", "test_id": "7", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/profile/8", "test_id": "8", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/profile/9", "test_id": "9", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/profile/10", "test_id": "10", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/profiles/1", "test_id": "1", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/profiles/2", "test_id": "2", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/profiles/3", "test_id": "3", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/profiles/4", "test_id": "4", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/profiles/5", "test_id": "5", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/profiles/6", "test_id": "6", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/profiles/7", "test_id": "7", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/profiles/8", "test_id": "8", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/profiles/9", "test_id": "9", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/profiles/10", "test_id": "10", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/v1/user/1", "test_id": "1", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/v1/user/2", "test_id": "2", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/v1/user/3", "test_id": "3", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/v1/user/4", "test_id": "4", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/v1/user/5", "test_id": "5", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/v1/user/6", "test_id": "6", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/v1/user/7", "test_id": "7", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/v1/user/8", "test_id": "8", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/v1/user/9", "test_id": "9", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/v1/user/10", "test_id": "10", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/v2/user/1", "test_id": "1", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\"></title><link rel=\"preconnect\" href=\"https://images-ssl.gotinder.com\"/><link rel=\"dns-prefetch\" href=\"https://api.gotinder.com\"/><link rel=\"dns-prefetch\" href=\"https://apis.google.com\"/><link rel=...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/v2/user/2", "test_id": "2", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/v2/user/3", "test_id": "3", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/v2/user/4", "test_id": "4", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/v2/user/5", "test_id": "5", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/v2/user/6", "test_id": "6", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/v2/user/7", "test_id": "7", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/v2/user/8", "test_id": "8", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/v2/user/9", "test_id": "9", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/v2/user/10", "test_id": "10", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/1/profile", "test_id": "1", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/2/profile", "test_id": "2", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/3/profile", "test_id": "3", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/4/profile", "test_id": "4", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/5/profile", "test_id": "5", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/6/profile", "test_id": "6", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/7/profile", "test_id": "7", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/8/profile", "test_id": "8", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/9/profile", "test_id": "9", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/10/profile", "test_id": "10", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/1/photos", "test_id": "1", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/2/photos", "test_id": "2", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/3/photos", "test_id": "3", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/4/photos", "test_id": "4", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/5/photos", "test_id": "5", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\"></title><link rel=\"preconnect\" href=\"https://images-ssl.gotinder.com\"/><link rel=\"dns-prefetch\" href=\"https://api.gotinder.com\"/><link rel=\"dns-prefetch\" href=\"https://apis.google.com\"/><link rel=...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/6/photos", "test_id": "6", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/7/photos", "test_id": "7", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\"></title><link rel=\"preconnect\" href=\"https://images-ssl.gotinder.com\"/><link rel=\"dns-prefetch\" href=\"https://api.gotinder.com\"/><link rel=\"dns-prefetch\" href=\"https://apis.google.com\"/><link rel=...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/8/photos", "test_id": "8", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/9/photos", "test_id": "9", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/10/photos", "test_id": "10", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/1/info", "test_id": "1", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/2/info", "test_id": "2", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/3/info", "test_id": "3", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/4/info", "test_id": "4", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/5/info", "test_id": "5", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\"></title><link rel=\"preconnect\" href=\"https://images-ssl.gotinder.com\"/><link rel=\"dns-prefetch\" href=\"https://api.gotinder.com\"/><link rel=\"dns-prefetch\" href=\"https://apis.google.com\"/><link rel=...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/6/info", "test_id": "6", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/7/info", "test_id": "7", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/8/info", "test_id": "8", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/9/info", "test_id": "9", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/10/info", "test_id": "10", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/1/details", "test_id": "1", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/2/details", "test_id": "2", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/3/details", "test_id": "3", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/4/details", "test_id": "4", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/5/details", "test_id": "5", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/6/details", "test_id": "6", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/7/details", "test_id": "7", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\"></title><link rel=\"preconnect\" href=\"https://images-ssl.gotinder.com\"/><link rel=\"dns-prefetch\" href=\"https://api.gotinder.com\"/><link rel=\"dns-prefetch\" href=\"https://apis.google.com\"/><link rel=...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/8/details", "test_id": "8", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/9/details", "test_id": "9", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://www.tinder.com/api/user/10/details", "test_id": "10", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/1", "test_id": "1", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/2", "test_id": "2", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/3", "test_id": "3", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/4", "test_id": "4", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/5", "test_id": "5", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/6", "test_id": "6", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/7", "test_id": "7", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/8", "test_id": "8", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/9", "test_id": "9", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/10", "test_id": "10", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/users/1", "test_id": "1", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/users/2", "test_id": "2", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/users/3", "test_id": "3", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/users/4", "test_id": "4", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/users/5", "test_id": "5", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/users/6", "test_id": "6", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/users/7", "test_id": "7", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/users/8", "test_id": "8", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/users/9", "test_id": "9", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/users/10", "test_id": "10", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/profile/1", "test_id": "1", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/profile/2", "test_id": "2", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/profile/3", "test_id": "3", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/profile/4", "test_id": "4", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/profile/5", "test_id": "5", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/profile/6", "test_id": "6", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/profile/7", "test_id": "7", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/profile/8", "test_id": "8", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/profile/9", "test_id": "9", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/profile/10", "test_id": "10", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/profiles/1", "test_id": "1", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/profiles/2", "test_id": "2", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/profiles/3", "test_id": "3", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/profiles/4", "test_id": "4", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/profiles/5", "test_id": "5", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/profiles/6", "test_id": "6", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/profiles/7", "test_id": "7", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/profiles/8", "test_id": "8", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/profiles/9", "test_id": "9", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/profiles/10", "test_id": "10", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/v1/user/1", "test_id": "1", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/v1/user/2", "test_id": "2", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/v1/user/3", "test_id": "3", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/v1/user/4", "test_id": "4", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/v1/user/5", "test_id": "5", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/v1/user/6", "test_id": "6", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/v1/user/7", "test_id": "7", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/v1/user/8", "test_id": "8", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/v1/user/9", "test_id": "9", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/v1/user/10", "test_id": "10", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/v2/user/1", "test_id": "1", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/v2/user/2", "test_id": "2", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/v2/user/3", "test_id": "3", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/v2/user/4", "test_id": "4", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/v2/user/5", "test_id": "5", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/v2/user/6", "test_id": "6", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/v2/user/7", "test_id": "7", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/v2/user/8", "test_id": "8", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/v2/user/9", "test_id": "9", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/v2/user/10", "test_id": "10", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/1/profile", "test_id": "1", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/2/profile", "test_id": "2", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/3/profile", "test_id": "3", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/4/profile", "test_id": "4", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/5/profile", "test_id": "5", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/6/profile", "test_id": "6", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/7/profile", "test_id": "7", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/8/profile", "test_id": "8", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/9/profile", "test_id": "9", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/10/profile", "test_id": "10", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/1/photos", "test_id": "1", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/2/photos", "test_id": "2", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/3/photos", "test_id": "3", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/4/photos", "test_id": "4", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/5/photos", "test_id": "5", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/6/photos", "test_id": "6", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/7/photos", "test_id": "7", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/8/photos", "test_id": "8", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/9/photos", "test_id": "9", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/10/photos", "test_id": "10", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/1/info", "test_id": "1", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/2/info", "test_id": "2", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/3/info", "test_id": "3", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/4/info", "test_id": "4", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/5/info", "test_id": "5", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/6/info", "test_id": "6", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/7/info", "test_id": "7", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/8/info", "test_id": "8", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/9/info", "test_id": "9", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/10/info", "test_id": "10", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/1/details", "test_id": "1", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/2/details", "test_id": "2", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/3/details", "test_id": "3", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/4/details", "test_id": "4", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/5/details", "test_id": "5", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/6/details", "test_id": "6", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/7/details", "test_id": "7", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/8/details", "test_id": "8", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/9/details", "test_id": "9", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}, {"type": "IDOR - HTML Response with Sensitive Data", "url": "https://staging.tinder.com/api/user/10/details", "test_id": "10", "content_preview": "<!doctype html><html id=\"Tinder\" lang=\"en\" class=\"W(100%) Us(n)\" ><head><title data-react-helmet=\"true\">Tinder | Dating, Make Friends &amp; Meet New People</title><meta data-react-helmet=\"true\" name=\"charset\" content=\"utf-8\"/><meta data-react-helmet=\"true\" name=\"description\" content=\"With 55 billion...", "severity": "Medium", "verified": true}]}