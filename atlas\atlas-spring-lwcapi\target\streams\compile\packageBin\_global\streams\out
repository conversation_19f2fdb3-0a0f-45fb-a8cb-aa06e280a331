[0m[[0m[0mdebug[0m] [0m[0mPackaging C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-spring-lwcapi\target\scala-2.13\atlas-spring-lwcapi_2.13-1.8.0-SNAPSHOT.jar ...[0m
[0m[[0m[0mdebug[0m] [0m[0mInput file mappings:[0m
[0m[[0m[0mdebug[0m] [0m[0m	com[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-spring-lwcapi\target\scala-2.13\classes\com[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-spring-lwcapi\target\scala-2.13\classes\com\netflix[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-spring-lwcapi\target\scala-2.13\classes\com\netflix\atlas[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-spring-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\lwcapi\LwcApiConfiguration.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-spring-lwcapi\target\scala-2.13\classes\com\netflix\atlas\lwcapi\LwcApiConfiguration.class[0m
[0m[[0m[0mdebug[0m] [0m[0mDone packaging.[0m
