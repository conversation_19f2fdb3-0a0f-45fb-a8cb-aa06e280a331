atlas {
  lwcapi {
    register = {
      default-step = 60s
    }

    # For an lwcapi instance to be ready to receive data, the consumer clients must have
    # an opportunity to connect first. This delay is used to control the window of time
    # they have to connnect and subscribe. The service will be marked as unhealthy until
    # this time has passed so that data from the producers will not yet be pushed through
    # the new instances.
    startup-delay = 3m

    # Size of the queue for each subscription stream. Data will be dropped if it is coming
    # in faster than it can be written out.
    queue-size = 100

    # Maximum number of messages to batch before sending back to the client.
    batch-size = 100

    # Should new or old messages get dropped if the queue is full?
    drop-new = true

    # How long to keep cached copies of encoded expressions
    exprs-ttl = 5m

    # When retrieving cluster data indicate if expressions using the publish step should be
    # ignored. These are filtered out on the client side when using a bridge, enabling this
    # can significantly reduce the response sizes if there are many poorly scoped expressions.
    exprs-cluster-ignore-publish-step = false
  }

  pekko {
    api-endpoints = ${?atlas.pekko.api-endpoints} [
      "com.netflix.atlas.lwcapi.EvaluateApi",
      "com.netflix.atlas.lwcapi.ExpressionApi",
      "com.netflix.atlas.lwcapi.StreamsApi",
      "com.netflix.atlas.lwcapi.SubscribeApi"
    ]
  }
}
