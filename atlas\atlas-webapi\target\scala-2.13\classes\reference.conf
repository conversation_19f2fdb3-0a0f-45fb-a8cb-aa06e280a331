
atlas {
  environment {
    ami = ${?EC2_AMI_ID}
    vmtype = ${?EC2_INSTANCE_TYPE}
    vpcId = ${?EC2_VPC_ID}

    region = "us-nflx-1"
    region = ${?EC2_REGION}

    zone = "us-nflx-1a"
    zone = ${?EC2_AVAILABILITY_ZONE}

    instanceId = ${?EC2_INSTANCE_ID}

    app = "local"
    app = ${?NETFLIX_APP}

    cluster = ${atlas.environment.app}
    cluster = ${?NETFLIX_CLUSTER}

    asg = ${atlas.environment.app}
    asg = ${?NETFLIX_AUTO_SCALE_GROUP}

    stack = "none"
    stack = ${?NETFLIX_STACK}

    account = "dev"
    account = ${?NETFLIX_ENVIRONMENT}
  }

  core.model {
    step = 1 minute
  }

  webapi {

    tags {
      max-limit = 1000
    }

    graph {
      start-time = e-3h
      end-time = now
      timezone = US/Pacific
      width = 700
      height = 300
      palette = armytage

      // Don't permit more that 1440 datapoints (1 day at minute resolution) for a single graph
      max-datapoints = 1440

      // Set of output formats to support via the graph API
      engines = [
        "com.netflix.atlas.chart.CommaSepGraphEngine",
        "com.netflix.atlas.chart.TabSepGraphEngine",
        "com.netflix.atlas.chart.JsonGraphEngine",
        "com.netflix.atlas.chart.StatsJsonGraphEngine",
        "com.netflix.atlas.chart.StdJsonGraphEngine",
        "com.netflix.atlas.chart.V2JsonGraphEngine",
        "com.netflix.atlas.chart.DefaultGraphEngine"
      ]

      // Vocabulary to use for the graph API. The value can be "default" or a class representing
      // the vocabulary to use.
      vocabulary = "default"

      // If set to true, the graph uri will be encoded as a Source iTXt field in the generated
      // png image. This can be useful for debugging and possibly other tooling, but increases
      // the image sizes and may not be desirable if the image may be shared externally.
      png-metadata-enabled = false
    }

    fetch {
      chunk-size = 60
    }

    publish {
      // Should we try to intern strings and tag values while parsing the request?
      intern-while-parsing = true

      // Validation rules to apply before accepting input data
      rules = [
        {
          class = "com.netflix.atlas.core.validation.HasKeyRule"
          key = "name"
        },
        {
          class = "com.netflix.atlas.core.validation.KeyLengthRule"
          min-length = 2
          max-length = 60
        },
        {
          class = "com.netflix.atlas.core.validation.NameValueLengthRule"
          name {
            min-length = 2
            max-length = 255
          }
          others {
            min-length = 1
            max-length = 120
          }
        },
        {
          class = "com.netflix.atlas.core.validation.ValidCharactersRule"
          default-pattern = "-._A-Za-z0-9^~"
          overrides = []
        },
        {
          class = "com.netflix.atlas.core.validation.MaxUserTagsRule"
          limit = 20
        },
        {
          class = "com.netflix.atlas.core.validation.ReservedKeyRule"
          prefix = "atlas."
          allowed-keys = [
            "aggr",
            "dstype",
            "offset",
            "legacy"
          ]
        },
        {
          class = "com.netflix.atlas.core.validation.ReservedKeyRule"
          prefix = "nf."
          allowed-keys = [
            "account",
            "ami",
            "app",
            "asg",
            "cluster",
            "container",
            "country",
            "country.rollup",
            "job",
            "node",
            "process",
            "region",
            "shard1",
            "shard2",
            "stack",
            "subnet",
            "task",
            "vmtype",
            "vpc",
            "zone"
          ]
        }
      ]

      // Maximum number of tags permitted per datapoint. No testing has been done on this
      // for numbers larger than 30.
      max-permitted-tags = 30

      // Max age for a datapoint. By default it is one step interval. If the timestamps are
      // normalized locally on the client 2 times the step is likely more appropriate.
      max-age = ${atlas.core.model.step}
    }

    expr {
      complete {
        // Words that are excluded from the list returned by the auto-completion suggestions.
        // These typically are either ones that always match, such as :depth or :true, or are
        // considered deprecated and should not be recommended to users.
        excluded-words = [
          // Stack manipulation
          "-rot",
          "2over",
          "call",
          "clear",
          "depth",
          "drop",
          "dup",
          "each",
          "fcall",
          "format",
          "freeze",
          "get",
          "list",
          "map",
          "ndrop",
          "nip",
          "nlist",
          "over",
          "pick",
          "roll",
          "rot",
          "set",
          "sset",
          "swap",
          "tuck",

          // Queries
          "true",
          "false",
          "reic",
          "not",

          // Data aggregations
          "all",
          "cf-avg",
          "cf-sum",
          "cf-min",
          "cf-max",
          "head",

          // Math
          "des",
          "des-simple",
          "des-fast",
          "des-slow",
          "des-slower",
          "des-epic-signal",
          "des-epic-viz",
          "random"

          // Filter
          "stat-avg",
          "stat-max",
          "stat-min",
          "stat-last",
          "stat-count",
          "stat-total",
          "stat-min-mf",
          "stat-max-mf",
          "stat-avg-mf"
        ]
      }
    }
  }

  pekko {
    actors = ${?atlas.pekko.actors} [
      {
        name = "db"
        class = "com.netflix.atlas.webapi.LocalDatabaseActor"
      }
    ]

    api-endpoints = ${?atlas.pekko.api-endpoints} [
      "com.netflix.atlas.webapi.TagsApi",
      "com.netflix.atlas.webapi.GraphApi",
      "com.netflix.atlas.webapi.ExprApi"
    ]
  }
}

