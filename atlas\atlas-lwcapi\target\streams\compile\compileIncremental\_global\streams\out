[0m[[0m[0mdebug[0m] [0m[0m[zinc] IncrementalCompile -----------[0m
[0m[[0m[0mdebug[0m] [0m[0mIncrementalCompile.incrementalCompile[0m
[0m[[0m[0mdebug[0m] [0m[0mprevious = Stamps for: 55 products, 15 sources, 19 libraries[0m
[0m[[0m[0mdebug[0m] [0m[0mcurrent source = Set(${BASE}/atlas-lwcapi/src/main/scala/com/netflix/atlas/lwcapi/package.scala, ${BASE}/atlas-lwcapi/src/main/scala/com/netflix/atlas/lwcapi/EvaluateApi.scala, ${BASE}/atlas-lwcapi/src/main/scala/com/netflix/atlas/lwcapi/StreamsApi.scala, ${BASE}/atlas-lwcapi/src/main/scala/com/netflix/atlas/lwcapi/QueueHandler.scala, ${BASE}/atlas-lwcapi/src/main/scala/com/netflix/atlas/lwcapi/SubscribeApi.scala, ${BASE}/atlas-lwcapi/src/main/scala/com/netflix/atlas/lwcapi/StartupDelayService.scala, ${BASE}/atlas-lwcapi/src/main/scala/com/netflix/atlas/lwcapi/WebSocketSessionManager.scala, ${BASE}/atlas-lwcapi/src/main/scala/com/netflix/atlas/lwcapi/ExpressionSplitter.scala, ${BASE}/atlas-lwcapi/src/main/scala/com/netflix/atlas/lwcapi/StreamMetadata.scala, ${BASE}/atlas-lwcapi/src/main/scala/com/netflix/atlas/lwcapi/ExpressionMetadata.scala, ${BASE}/atlas-lwcapi/src/main/scala/com/netflix/atlas/lwcapi/Subscription.scala, ${BASE}/atlas-lwcapi/src/main/scala/com/netflix/atlas/lwcapi/StreamSubscriptionManager.scala, ${BASE}/atlas-lwcapi/src/main/scala/com/netflix/atlas/lwcapi/ExpressionApi.scala, ${BASE}/atlas-lwcapi/src/main/scala/com/netflix/atlas/lwcapi/SubscriptionManager.scala, ${BASE}/atlas-lwcapi/src/main/scala/com/netflix/atlas/lwcapi/ApiSettings.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m> initialChanges = InitialChanges(Changes(added = Set(), removed = Set(), changed = Set(), unmodified = ...),Set(),Set(),API Changes: Set())[0m
[0m[[0m[0mdebug[0m] [0m[0mNo changes[0m
