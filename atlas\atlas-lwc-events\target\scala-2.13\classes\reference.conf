
atlas.lwc.events {

  base-uri = "http://localhost:7101/lwc/api/v1"
  config-uri = ${atlas.lwc.events.base-uri}"/expressions"
  eval-uri = ${atlas.lwc.events.base-uri}"/evaluate"

  // Frequency for refreshing configs
  config-refresh-frequency = 10s

  // Frequency for sending heartbeats
  heartbeat-frequency = 2s

  // Max buffer size before events will start getting dropped. Used to avoid OOM.
  buffer-size = 1000000

  // Buffer size to force a flush before regular time interval.
  flush-size = 100000

  // Batch size for publishing data
  batch-size = 5000

  // Max payload size for events data. Default max payload size for server is 8MiB.
  payload-size = 7MiB

  // Maximum size for a group by. Used to avoid OOM for group by with a high cardinality dimension.
  // If exceeded new groups will be dropped.
  max-groups = 10000
}
