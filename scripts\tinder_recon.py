#!/usr/bin/env python3
"""
Enhanced Reconnaissance Script for Tinder Security Testing
This script performs comprehensive reconnaissance without requiring external tools
Focuses on avoiding false positives and thorough asset discovery
"""

import requests
import dns.resolver
import socket
import ssl
import json
import sys
import time
import threading
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
import subprocess
import os

class TinderRecon:
    def __init__(self, domain):
        self.domain = domain
        self.subdomains = set()
        self.alive_domains = []
        self.technologies = {}
        self.open_ports = {}
        self.results_dir = f"tinder_recon_{domain}"
        
        # Create results directory
        os.makedirs(self.results_dir, exist_ok=True)
        
        # Common subdomains for dating apps
        self.common_subdomains = [
            'www', 'api', 'mobile', 'app', 'admin', 'dev', 'test', 'staging', 'beta',
            'cdn', 'static', 'assets', 'images', 'media', 'upload', 'downloads',
            'mail', 'email', 'smtp', 'pop', 'imap', 'webmail',
            'ftp', 'sftp', 'ssh', 'vpn', 'remote',
            'blog', 'news', 'help', 'support', 'docs', 'wiki',
            'shop', 'store', 'payment', 'billing', 'checkout',
            'auth', 'login', 'sso', 'oauth', 'accounts',
            'dashboard', 'panel', 'control', 'manage',
            'analytics', 'stats', 'metrics', 'tracking',
            'chat', 'message', 'notification', 'push',
            'profile', 'user', 'users', 'member', 'members',
            'match', 'matches', 'swipe', 'like', 'likes',
            'premium', 'plus', 'gold', 'subscription', 'billing',
            'geo', 'location', 'maps', 'nearby',
            'video', 'call', 'voice', 'stream',
            'security', 'privacy', 'legal', 'terms',
            'm', 'mobile-api', 'api-v1', 'api-v2', 'v1', 'v2', 'v3',
            'internal', 'private', 'secure', 'protected'
        ]
        
        # Bug bounty research header
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'X-HackerOne-Research': 'datafae'
        }
        
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def print_status(self, message, status="INFO"):
        colors = {
            "INFO": "\033[94m",
            "SUCCESS": "\033[92m", 
            "WARNING": "\033[93m",
            "ERROR": "\033[91m",
            "RESET": "\033[0m"
        }
        print(f"{colors.get(status, '')}{message}{colors['RESET']}")
    
    def check_subdomain(self, subdomain):
        """Check if a subdomain exists and is alive"""
        full_domain = f"{subdomain}.{self.domain}"
        
        try:
            # DNS resolution check
            dns.resolver.resolve(full_domain, 'A')
            self.subdomains.add(full_domain)
            
            # HTTP/HTTPS connectivity check
            for protocol in ['https', 'http']:
                try:
                    url = f"{protocol}://{full_domain}"
                    response = self.session.get(url, timeout=10, allow_redirects=True)
                    
                    if response.status_code < 400:
                        self.alive_domains.append({
                            'domain': full_domain,
                            'url': url,
                            'status_code': response.status_code,
                            'title': self.extract_title(response.text),
                            'server': response.headers.get('Server', 'Unknown'),
                            'content_length': len(response.content)
                        })
                        self.print_status(f"[+] Found alive domain: {url} (Status: {response.status_code})", "SUCCESS")
                        break
                        
                except requests.exceptions.RequestException:
                    continue
                    
        except (dns.resolver.NXDOMAIN, dns.resolver.NoAnswer, Exception):
            pass
    
    def extract_title(self, html_content):
        """Extract title from HTML content"""
        try:
            import re
            title_match = re.search(r'<title[^>]*>([^<]+)</title>', html_content, re.IGNORECASE)
            return title_match.group(1).strip() if title_match else "No Title"
        except:
            return "No Title"
    
    def enumerate_subdomains(self):
        """Enumerate subdomains using common subdomain list"""
        self.print_status("[*] Starting subdomain enumeration...", "INFO")
        
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(self.check_subdomain, sub) for sub in self.common_subdomains]
            
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    pass
        
        self.print_status(f"[+] Found {len(self.subdomains)} subdomains", "SUCCESS")
        self.print_status(f"[+] Found {len(self.alive_domains)} alive domains", "SUCCESS")
    
    def check_common_ports(self, domain):
        """Check common ports on a domain"""
        common_ports = [21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995, 8080, 8443, 3389]
        open_ports = []
        
        for port in common_ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(3)
                result = sock.connect_ex((domain, port))
                if result == 0:
                    open_ports.append(port)
                sock.close()
            except:
                pass
        
        return open_ports
    
    def analyze_technologies(self, domain_info):
        """Analyze technologies used by the domain"""
        try:
            response = self.session.get(domain_info['url'], timeout=10)
            
            tech_indicators = {
                'React': ['react', '_react', 'react-dom'],
                'Angular': ['ng-', 'angular', '_angular'],
                'Vue.js': ['vue', '_vue', 'v-'],
                'jQuery': ['jquery', '$'],
                'Bootstrap': ['bootstrap', 'btn-'],
                'Express': ['x-powered-by: express'],
                'Nginx': ['server: nginx'],
                'Apache': ['server: apache'],
                'Cloudflare': ['cf-ray', 'cloudflare'],
                'AWS': ['x-amz-', 'amazonaws'],
                'PHP': ['x-powered-by: php', '.php'],
                'ASP.NET': ['x-aspnet-version', 'asp.net'],
                'Node.js': ['x-powered-by: express', 'node.js']
            }
            
            detected_tech = []
            content = response.text.lower()
            headers = str(response.headers).lower()
            
            for tech, indicators in tech_indicators.items():
                for indicator in indicators:
                    if indicator in content or indicator in headers:
                        detected_tech.append(tech)
                        break
            
            return detected_tech
            
        except:
            return []
    
    def generate_report(self):
        """Generate comprehensive reconnaissance report"""
        report = {
            'domain': self.domain,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'subdomains_found': len(self.subdomains),
            'alive_domains': len(self.alive_domains),
            'subdomains': list(self.subdomains),
            'alive_domain_details': self.alive_domains,
            'summary': {
                'total_subdomains': len(self.subdomains),
                'alive_domains': len(self.alive_domains),
                'unique_technologies': len(set([tech for domain in self.alive_domains for tech in domain.get('technologies', [])]))
            }
        }
        
        # Save JSON report
        with open(f"{self.results_dir}/reconnaissance_report.json", 'w') as f:
            json.dump(report, f, indent=2)
        
        # Generate markdown report
        md_report = f"""# Tinder Security Reconnaissance Report
Generated on: {report['timestamp']}
Target Domain: {self.domain}

## Summary
- **Total Subdomains Found**: {report['subdomains_found']}
- **Alive Domains**: {report['alive_domains']}

## Alive Domains
"""
        
        for domain in self.alive_domains:
            md_report += f"""
### {domain['domain']}
- **URL**: {domain['url']}
- **Status Code**: {domain['status_code']}
- **Title**: {domain['title']}
- **Server**: {domain['server']}
- **Content Length**: {domain['content_length']} bytes
"""
        
        md_report += f"""
## All Discovered Subdomains
"""
        for subdomain in sorted(self.subdomains):
            md_report += f"- {subdomain}\n"
        
        with open(f"{self.results_dir}/reconnaissance_report.md", 'w') as f:
            f.write(md_report)
        
        self.print_status(f"[+] Reports saved to {self.results_dir}/", "SUCCESS")
    
    def run(self):
        """Run the complete reconnaissance process"""
        self.print_status(f"[*] Starting Tinder reconnaissance for {self.domain}", "INFO")
        
        # Step 1: Subdomain enumeration
        self.enumerate_subdomains()
        
        # Step 2: Technology analysis for alive domains
        self.print_status("[*] Analyzing technologies...", "INFO")
        for domain in self.alive_domains:
            domain['technologies'] = self.analyze_technologies(domain)
            domain['open_ports'] = self.check_common_ports(domain['domain'])
        
        # Step 3: Generate report
        self.generate_report()
        
        self.print_status("[+] Reconnaissance completed!", "SUCCESS")
        return self.alive_domains

def main():
    if len(sys.argv) != 2:
        print("Usage: python tinder_recon.py <domain>")
        sys.exit(1)
    
    domain = sys.argv[1]
    recon = TinderRecon(domain)
    recon.run()

if __name__ == "__main__":
    main()
