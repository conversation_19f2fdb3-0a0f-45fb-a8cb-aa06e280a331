{"{\"organization\":\"com.typesafe\",\"name\":\"config\",\"revision\":\"1.4.3\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\build.sbt", "startLine": 127}, "type": "LinePosition"}, "{\"organization\":\"com.typesafe.scala-logging\",\"name\":\"scala-logging\",\"revision\":\"3.9.5\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\build.sbt", "startLine": 127}, "type": "LinePosition"}, "{\"organization\":\"org.scalameta\",\"name\":\"munit\",\"revision\":\"1.1.1\",\"configurations\":\"test\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\build.sbt", "startLine": 127}, "type": "LinePosition"}, "{\"organization\":\"com.netflix.spectator\",\"name\":\"spectator-api\",\"revision\":\"1.8.14\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\build.sbt", "startLine": 127}, "type": "LinePosition"}, "{\"organization\":\"org.slf4j\",\"name\":\"slf4j-api\",\"revision\":\"2.0.17\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\build.sbt", "startLine": 127}, "type": "LinePosition"}, "{\"organization\":\"com.netflix.iep\",\"name\":\"iep-spring\",\"revision\":\"5.1.2\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\build.sbt", "startLine": 127}, "type": "LinePosition"}, "{\"organization\":\"org.scala-lang\",\"name\":\"scala-library\",\"revision\":\"2.13.16\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\build.sbt", "startLine": 127}, "type": "LinePosition"}, "{\"organization\":\"org.scala-lang.modules\",\"name\":\"scala-collection-compat\",\"revision\":\"2.13.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\build.sbt", "startLine": 127}, "type": "LinePosition"}, "{\"organization\":\"com.google.code.findbugs\",\"name\":\"jsr305\",\"revision\":\"3.0.2\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "startLine"], "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\build.sbt", "startLine": 127}, "type": "LinePosition"}}