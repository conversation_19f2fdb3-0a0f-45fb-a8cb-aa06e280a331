["sbt.Task[scala.collection.Seq[java.nio.file.Path]]", ["C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\LegendEntry.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\HorizontalPadding.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\Legend$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\Colors$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\TimeSeriesSpan.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\util\\SrcPath$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\TickLabelMode.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\PlotDef.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\Ticks$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\MessageDef$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\VariableHeight.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\HSpanDef$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\util\\Fonts.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\ListItem.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\StatsJsonGraphEngine.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\util\\SrcPath.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\TimeSeriesLine.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\LogLinear$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\CommaSepGraphEngine.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\StdJsonGraphEngine.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\GraphDef.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\ChartSettings.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\Style.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\PlotBound$AutoData$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\LegendType.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\TimeGrid$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\PlotDef$$anonfun$horizontalSpans$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\ValueAxis$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\ChartSettings$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\FixedWidth.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\TimeSeriesGraph$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\Heatmap$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\ValueTick$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\TimeSeriesArea.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\Ticks.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\HeatmapLegendEntry.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\TimeSeriesHeatmap.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\RightValueAxis.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\GraphDef$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\PngGraphEngine.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\TimeTick$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\Scales$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\HeatmapDef.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\RightValueAxis$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\JsonGraphEngine.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\JsonCodec.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\Styles.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\V2JsonGraphEngine.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\TimeSeriesHeatmap$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\Palette$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\HeatmapLegendEntry$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\LineDef.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\TimeAxis$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\GraphConstants$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\PlotBound.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\VSpanDef.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\TimeSeriesStack$Offsets$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\TimeSeriesGraph.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\util\\PngImage$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\TimeTick.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\Scale.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\VisionType.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\GraphEngine$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\LineStyle.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\Element.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\PlotBound$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\Theme$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\LineDef$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\TimeGrid.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\ValueGrid$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\ValueAxis.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\TimeSeriesStack.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\ValueGrid.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\ListItem$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\PlotBound$Explicit.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\HorizontalPadding$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\Block$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\TimeSeriesLine$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\ValueSpan$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\util\\PngImage$ReleasableImageWriter$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\TabSepGraphEngine.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\PlotDef$$anonfun$heatmapLines$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\Palette$$anon$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\PlotBound$Explicit$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\LegendEntry$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\FixedHeight.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\MessageDef.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\TimeSeriesStack$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\ChartSettings$Dimensions$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\CsvGraphEngine.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\util\\PngImage.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\Theme.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\Heatmap.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\Legend.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\TimeSpan$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\JsonCodec$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\LeftValueAxis$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\VSpanDef$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\TextAlignment.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\Layout.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\ValueSpan.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\Style$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\TimeAxis.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\TimeSeriesStack$Offsets.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\Block.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\HeatmapDef$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\Text$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\Text.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\ChartSettings$Dimensions.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\GraphConstants.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\Palette.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\util\\GraphAssertions.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\TimeSpan.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\PlotDef$$anonfun$verticalSpans$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\DataDef.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\Colors.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\util\\Fonts$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\LeftValueAxis.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\GraphEngine.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\PlotDef$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\Scales.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\PlotDef$$anonfun$lines$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\DefaultGraphEngine.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\TimeSeriesArea$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\PlotBound$AutoStyle$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\Styles$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\TimeSeriesSpan$.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\ValueTick.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\HSpanDef.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\model\\PlotDef$$anonfun$renderedLines$1.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\LogLinear.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\classes\\com\\netflix\\atlas\\chart\\graphics\\VariableWidth.class", "C:\\Users\\<USER>\\Documents\\augment-projects\\security\\atlas\\atlas-chart\\target\\scala-2.13\\zinc\\inc_compile_2.13.zip"]]