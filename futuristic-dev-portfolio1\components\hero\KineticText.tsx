'use client'
import { useEffect, useRef } from 'react'
import { gsap } from 'gsap'

interface KineticTextProps {
  text: string
  className?: string
  delay?: number
}

export default function KineticText({ text, className = '', delay = 0 }: KineticTextProps) {
  const textRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!textRef.current) return

    const chars = textRef.current.querySelectorAll('.char')
    
    // Initial state - characters scattered and invisible
    gsap.set(chars, {
      opacity: 0,
      y: 100,
      rotation: 45,
      scale: 0.5,
    })

    // Animate characters into position
    gsap.to(chars, {
      opacity: 1,
      y: 0,
      rotation: 0,
      scale: 1,
      duration: 1.2,
      ease: 'back.out(1.7)',
      stagger: {
        amount: 0.8,
        from: 'random'
      },
      delay: delay,
    })

    // Add continuous glitch effect
    const glitchTimeline = gsap.timeline({ repeat: -1, repeatDelay: 3 })
    glitchTimeline
      .to(chars, {
        x: () => Math.random() * 4 - 2,
        duration: 0.1,
        stagger: 0.02,
      })
      .to(chars, {
        x: 0,
        duration: 0.1,
        stagger: 0.02,
      })

  }, [delay])

  const splitText = text.split('').map((char, index) => (
    <span
      key={index}
      className="char inline-block"
      style={{
        textShadow: '0 0 10px currentColor',
      }}
    >
      {char === ' ' ? '\u00A0' : char}
    </span>
  ))

  return (
    <div
      ref={textRef}
      className={`font-cyber font-bold ${className}`}
      style={{
        background: 'linear-gradient(45deg, #00f5ff, #bf00ff, #ff0080)',
        backgroundClip: 'text',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        filter: 'drop-shadow(0 0 20px rgba(0, 245, 255, 0.5))',
      }}
    >
      {splitText}
    </div>
  )
}