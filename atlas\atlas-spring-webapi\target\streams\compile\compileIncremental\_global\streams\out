[0m[[0m[0mdebug[0m] [0m[0m[zinc] IncrementalCompile -----------[0m
[0m[[0m[0mdebug[0m] [0m[0mIncrementalCompile.incrementalCompile[0m
[0m[[0m[0mdebug[0m] [0m[0mprevious = Stamps for: 1 products, 1 sources, 3 libraries[0m
[0m[[0m[0mdebug[0m] [0m[0mcurrent source = Set(${BASE}/atlas-spring-webapi/src/main/scala/com/netflix/atlas/webapi/WebApiConfiguration.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m> initialChanges = InitialChanges(Changes(added = Set(), removed = Set(), changed = Set(), unmodified = ...),Set(),Set(),API Changes: Set())[0m
[0m[[0m[0mdebug[0m] [0m[0mNo changes[0m
