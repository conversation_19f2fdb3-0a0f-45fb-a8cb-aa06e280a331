{"domain": "tinder.com", "timestamp": "2025-06-28 19:34:55", "total_subdomains": 2, "alive_domains": 2, "subdomains": ["staging.tinder.com", "www.tinder.com"], "alive_domain_details": [{"domain": "staging.tinder.com", "url": "https://staging.tinder.com", "status_code": 200, "title": "Tinder | Dating, Make Friends &amp; Meet New People", "server": "nginx", "content_length": 470840, "headers": {"Content-Type": "text/html; charset=utf-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "Date": "Sat, 28 Jun 2025 18:34:56 GMT", "Set-Cookie": "AWSALB=u7bYLzGMP4+XpKXyeKMipAmoEYBmdqspszCCvVjiukxocnT4Kgt+xzfwcdZgziofhHcSvGk0Bws60jbS8SlJPDz5pgpFufcr9v9cNkCW+GZPbke083Z8u3Aoa36d; Expires=Sat, 05 Jul 2025 18:34:56 GMT; Path=/, AWSALBCORS=u7bYLzGMP4+XpKXyeKMipAmoEYBmdqspszCCvVjiukxocnT4Kgt+xzfwcdZgziofhHcSvGk0Bws60jbS8SlJPDz5pgpFufcr9v9cNkCW+GZPbke083Z8u3Aoa36d; Expires=Sat, 05 Jul 2025 18:34:56 GMT; Path=/; SameSite=None; Secure", "Server": "nginx", "X-Powered-By": "Express", "X-DNS-Prefetch-Control": "on", "Referrer-Policy": "origin-when-cross-origin", "X-Robots-Tag": "noindex", "Content-Security-Policy-Report-Only": "default-src 'self';base-uri 'self';connect-src 'self' wss://keepalive.gotinder.com wss://keepalive.ue1.d1.tstaging.com https://messagepublish.ue1.d4.tstaging.com https://*.gotinder.com https://*.appsflyer.com https://*.bugsnag.com https://tinder-api.arkoselabs.com https://*.spotify.com https://*.line.me https://*.onelink.me https://*.*.braintree-api.com https://*.*.paypal.com https://*.braintree-api.com https://*.paypal.com https://*.braintreegateway.com https://*.s3.amazonaws.com https://*.google-analytics.com https://*.analytics.google.com https://*.googletagmanager.com https://*.g.doubleclick.net https://*.doubleclick.net https://*.google.com https://maps.googleapis.com https://*.gstatic.com https://fonts.googleapis.com;script-src 'nonce-tSkcIPekQ0mJsmlfNOADRA==' 'strict-dynamic' 'unsafe-eval' 'unsafe-hashes' 'sha256-PLCxbpHSwAa8+W198R1KQQ9UDCexTvYy4z4YmCg21NM=' 'unsafe-inline' https:;style-src 'self' 'unsafe-inline' blob: https://*.google-analytics.com https://*.analytics.google.com https://*.googletagmanager.com https://*.g.doubleclick.net https://*.doubleclick.net https://*.google.com https://maps.googleapis.com https://*.gstatic.com https://fonts.googleapis.com;frame-src 'self' https://tinder-api.arkoselabs.com https://*.*.paypal.com https://*.paypal.com https://*.google-analytics.com https://*.analytics.google.com https://*.googletagmanager.com https://*.g.doubleclick.net https://*.doubleclick.net https://*.google.com https://maps.googleapis.com https://*.gstatic.com https://fonts.googleapis.com;frame-ancestors 'self';form-action 'self' *.tinder.com tinder.com;object-src 'none';img-src 'self' data: blob: https://*.gotinder.com https://*.scdn.co https://*.cdninstagram.com https://*.cloudfront.net https://*.s3.amazonaws.com https://*.*.paypal.com https://*.paypalobjects.com https://*.google-analytics.com https://*.analytics.google.com https://*.googletagmanager.com https://*.g.doubleclick.net https://*.doubleclick.net https://*.google.com https://maps.googleapis.com https://*.gstatic.com https://fonts.googleapis.com;media-src 'self' data: https://*.scdn.co https://*.spotify.com https://media.tenor.com;report-to csp-reports;font-src 'self' data: https:;manifest-src 'self' https:", "Content-Security-Policy": "default-src *;script-src * 'unsafe-inline' 'unsafe-eval';style-src * 'unsafe-inline' blob:;img-src * data: blob:;media-src * data:;font-src * data: https:", "Report-To": "{\"group\":\"csp-reports\",\"max_age\":604800,\"endpoints\":[{\"url\":\"/api/csp-reports\"}]}", "X-Render-Method": "ssr", "Cache-Control": "must-revalidate, public, max-age=3024000000", "Cross-Origin-Opener-Policy": "same-origin-allow-popups", "ETag": "W/\"72f38-+5uQHyQOoPLuWQ8KPYuOfIdhwJE\"", "Content-Encoding": "gzip", "Vary": "Accept-Encoding", "X-Cache": "Miss from cloudfront", "Via": "1.1 801f161811c7af839461382eb62af1dc.cloudfront.net (CloudFront)", "X-Amz-Cf-Pop": "LHR61-P1", "X-Amz-Cf-Id": "_NKeWYDASd9mi2-FlMd-fCj6CIwuSG7JbsP2zg1bLmTfUqLpDgBHLg==", "X-XSS-Protection": "1; mode=block", "X-Frame-Options": "SAMEORIGIN", "X-Content-Type-Options": "nosniff", "Strict-Transport-Security": "max-age=31536000"}}, {"domain": "www.tinder.com", "url": "https://www.tinder.com", "status_code": 200, "title": "Tinder | Dating, Make Friends &amp; Meet New People", "server": "nginx", "content_length": 470459, "headers": {"Content-Type": "text/html; charset=utf-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "Date": "Sat, 28 Jun 2025 18:34:56 GMT", "Content-Encoding": "gzip", "Set-Cookie": "AWSALB=Bi8LzNz0e02LMS2jbP8qx2rq9AhesFBle7BaH8DyAmOhepOrTYRHICwkDV/H7/96sn2u3Ybh+F1weZiq8UyvaW0zg4QV+zGZiGT7nR7jGGVaqD6w/pF3CLl1j/h6; Expires=Sat, 05 Jul 2025 18:34:56 GMT; Path=/, AWSALBCORS=Bi8LzNz0e02LMS2jbP8qx2rq9AhesFBle7BaH8DyAmOhepOrTYRHICwkDV/H7/96sn2u3Ybh+F1weZiq8UyvaW0zg4QV+zGZiGT7nR7jGGVaqD6w/pF3CLl1j/h6; Expires=Sat, 05 Jul 2025 18:34:56 GMT; Path=/; SameSite=None; Secure", "Server": "nginx", "X-Powered-By": "Express", "X-DNS-Prefetch-Control": "on", "Referrer-Policy": "origin-when-cross-origin", "Content-Security-Policy-Report-Only": "default-src 'self';base-uri 'self';connect-src 'self' wss://keepalive.gotinder.com wss://keepalive.ue1.d1.tstaging.com https://messagepublish.ue1.d4.tstaging.com https://*.gotinder.com https://*.appsflyer.com https://*.bugsnag.com https://tinder-api.arkoselabs.com https://*.spotify.com https://*.line.me https://*.onelink.me https://*.*.braintree-api.com https://*.*.paypal.com https://*.braintree-api.com https://*.paypal.com https://*.braintreegateway.com https://*.s3.amazonaws.com https://*.google-analytics.com https://*.analytics.google.com https://*.googletagmanager.com https://*.g.doubleclick.net https://*.doubleclick.net https://*.google.com https://maps.googleapis.com https://*.gstatic.com https://fonts.googleapis.com;script-src 'nonce-PFmztv7p3h4oQw5GaVqi8Q==' 'strict-dynamic' 'unsafe-eval' 'unsafe-hashes' 'sha256-PLCxbpHSwAa8+W198R1KQQ9UDCexTvYy4z4YmCg21NM=' 'unsafe-inline' https:;style-src 'self' 'unsafe-inline' blob: https://*.google-analytics.com https://*.analytics.google.com https://*.googletagmanager.com https://*.g.doubleclick.net https://*.doubleclick.net https://*.google.com https://maps.googleapis.com https://*.gstatic.com https://fonts.googleapis.com;frame-src 'self' https://tinder-api.arkoselabs.com https://*.*.paypal.com https://*.paypal.com https://*.google-analytics.com https://*.analytics.google.com https://*.googletagmanager.com https://*.g.doubleclick.net https://*.doubleclick.net https://*.google.com https://maps.googleapis.com https://*.gstatic.com https://fonts.googleapis.com;frame-ancestors 'self';form-action 'self' *.tinder.com tinder.com;object-src 'none';img-src 'self' data: blob: https://*.gotinder.com https://*.scdn.co https://*.cdninstagram.com https://*.cloudfront.net https://*.s3.amazonaws.com https://*.*.paypal.com https://*.paypalobjects.com https://*.google-analytics.com https://*.analytics.google.com https://*.googletagmanager.com https://*.g.doubleclick.net https://*.doubleclick.net https://*.google.com https://maps.googleapis.com https://*.gstatic.com https://fonts.googleapis.com;media-src 'self' data: https://*.scdn.co https://*.spotify.com https://media.tenor.com;report-to csp-reports;font-src 'self' data: https:;manifest-src 'self' https:", "Content-Security-Policy": "default-src *;script-src * 'unsafe-inline' 'unsafe-eval';style-src * 'unsafe-inline' blob:;img-src * data: blob:;media-src * data:;font-src * data: https:", "Report-To": "{\"group\":\"csp-reports\",\"max_age\":604800,\"endpoints\":[{\"url\":\"/api/csp-reports\"}]}", "X-Render-Method": "ssr", "Cache-Control": "must-revalidate, public, max-age=3600", "Cross-Origin-Opener-Policy": "same-origin-allow-popups", "ETag": "W/\"72dbb-qwpJt2xzZfVZhKhzTDViH4MiANE\"", "Vary": "Accept-Encoding", "X-Cache": "Miss from cloudfront", "Via": "1.1 87911f7f4ec2932c1f9ed5a36cc017ae.cloudfront.net (CloudFront)", "X-Amz-Cf-Pop": "LHR61-P1", "X-Amz-Cf-Id": "cv5nS54h7TRygS_HGAsmhWRYqMywQynjsqd50dDXjRyqVFkqNA6VGA==", "X-XSS-Protection": "1; mode=block", "X-Frame-Options": "SAMEORIGIN", "X-Content-Type-Options": "nosniff", "Strict-Transport-Security": "max-age=31536000"}}]}