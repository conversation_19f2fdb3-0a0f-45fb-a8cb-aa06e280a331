
atlas {
  chart {
    // Allows arbitrary rendering hints to be set on the graphics object used for the chart.
    // See the javadocs for more information on available options:
    // http://docs.oracle.com/javase/7/docs/api/java/awt/RenderingHints.html
    rendering-hints {
      KEY_RENDERING = "VALUE_RENDER_SPEED"
      KEY_TEXT_ANTIALIASING = "VALUE_TEXT_ANTIALIAS_LCD_HRGB"
    }

    // Control the base fonts used. This setting is to allow for some experimentation and may
    // go away if moved into per-chart options.
    //
    // To ensure consistent rendering for tests across versions of the JDK and operating
    // systems we include the Apache licensed RobotoMono font.
    fonts {
      monospace = "fonts/RobotoMono-Regular.ttf"
    }

    theme {
      default = "light"

      light {
        image {
          background-color = "FFF5F5F5"
          background-stroke = "solid"
          line-color = "FF000000"
          line-stroke = "solid"
          text-color = "FF000000"
          text-stroke = "solid"
        }
        canvas = ${atlas.chart.theme.light.image} {
          background-color = "FFFFFFFF"
        }
        minor-grid = ${atlas.chart.theme.light.image} {
          line-color = "5FABABAB"
          line-stroke = "dashed"
        }
        major-grid = ${atlas.chart.theme.light.image} {
          line-color = "5FFF5B5B"
          line-stroke = "dashed"
        }
        axis = ${atlas.chart.theme.light.image}
        legend = ${atlas.chart.theme.light.canvas}
        warnings = ${atlas.chart.theme.light.image} {
          background-color = "FFFFC800"
        }
      }

      dark {
        image {
          background-color = "FF0A0A0A"
          background-stroke = "solid"
          line-color = "FFFFFFFF"
          line-stroke = "solid"
          text-color = "FFFFFFFF"
          text-stroke = "solid"
        }
        canvas = ${atlas.chart.theme.dark.image} {
          background-color = "FF000000"
        }
        minor-grid = ${atlas.chart.theme.dark.image} {
          line-color = "5FABABAB"
          line-stroke = "dashed"
        }
        major-grid = ${atlas.chart.theme.dark.image} {
          line-color = "5FFF5B5B"
          line-stroke = "dashed"
        }
        axis = ${atlas.chart.theme.dark.image}
        legend = ${atlas.chart.theme.dark.canvas}
        warnings = ${atlas.chart.theme.dark.image} {
          background-color = "FFFFC800"
          text-color = "FF000000"
        }
      }
    }

    // Limits on image rendering to ensure
    limits {
      min-canvas-width = 64
      min-canvas-height = 64

      max-width = 2000
      max-height = 1000
      max-zoom = 2.0
      max-lines-in-legend = 50
      max-yaxes = 5
    }
  }
}