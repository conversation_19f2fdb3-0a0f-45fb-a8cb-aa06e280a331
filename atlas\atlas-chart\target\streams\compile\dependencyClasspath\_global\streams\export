C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-core\target\scala-2.13\atlas-core_2.13-1.8.0-SNAPSHOT.jar;C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-json\target\scala-2.13\atlas-json_2.13-1.8.0-SNAPSHOT.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.16\scala-library-2.13.16.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-lang\modules\scala-collection-compat_2.13\2.13.0\scala-collection-compat_2.13-2.13.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\typesafe\scala-logging\scala-logging_2.13\3.9.5\scala-logging_2.13-3.9.5.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\slf4j\slf4j-api\2.0.17\slf4j-api-2.0.17.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\netflix\spectator\spectator-api\1.8.14\spectator-api-1.8.14.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\typesafe\config\1.4.3\config-1.4.3.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\github\ben-manes\caffeine\caffeine\3.2.0\caffeine-3.2.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\netflix\iep\iep-dynconfig\5.1.2\iep-dynconfig-5.1.2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\roaringbitmap\RoaringBitmap\1.3.0\RoaringBitmap-1.3.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\fasterxml\jackson\core\jackson-core\2.19.0\jackson-core-2.19.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.19.0\jackson-datatype-jdk8-2.19.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.19.0\jackson-datatype-jsr310-2.19.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\fasterxml\jackson\core\jackson-databind\2.19.0\jackson-databind-2.19.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\fasterxml\jackson\module\jackson-module-scala_2.13\2.19.0\jackson-module-scala_2.13-2.19.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\fasterxml\jackson\dataformat\jackson-dataformat-smile\2.19.0\jackson-dataformat-smile-2.19.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-reflect\2.13.16\scala-reflect-2.13.16.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\jspecify\jspecify\1.0.0\jspecify-1.0.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\google\errorprone\error_prone_annotations\2.36.0\error_prone_annotations-2.36.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\fasterxml\jackson\core\jackson-annotations\2.19.0\jackson-annotations-2.19.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\thoughtworks\paranamer\paranamer\2.8.3\paranamer-2.8.3.jar
