C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-spring-pekko\target\scala-2.13\atlas-spring-pekko_2.13-1.8.0-SNAPSHOT.jar;C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-pekko\target\scala-2.13\atlas-pekko_2.13-1.8.0-SNAPSHOT.jar;C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-json\target\scala-2.13\atlas-json_2.13-1.8.0-SNAPSHOT.jar;C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-spring-lwcapi\target\scala-2.13\atlas-spring-lwcapi_2.13-1.8.0-SNAPSHOT.jar;C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-lwcapi\target\scala-2.13\atlas-lwcapi_2.13-1.8.0-SNAPSHOT.jar;C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-core\target\scala-2.13\atlas-core_2.13-1.8.0-SNAPSHOT.jar;C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-eval\target\scala-2.13\atlas-eval_2.13-1.8.0-SNAPSHOT.jar;C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-chart\target\scala-2.13\atlas-chart_2.13-1.8.0-SNAPSHOT.jar;C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-spring-webapi\target\scala-2.13\atlas-spring-webapi_2.13-1.8.0-SNAPSHOT.jar;C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-webapi\target\scala-2.13\atlas-webapi_2.13-1.8.0-SNAPSHOT.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-library\2.13.16\scala-library-2.13.16.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-lang\modules\scala-collection-compat_2.13\2.13.0\scala-collection-compat_2.13-2.13.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\typesafe\scala-logging\scala-logging_2.13\3.9.5\scala-logging_2.13-3.9.5.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\slf4j\slf4j-api\2.0.17\slf4j-api-2.0.17.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\netflix\spectator\spectator-api\1.8.14\spectator-api-1.8.14.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\typesafe\config\1.4.3\config-1.4.3.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\netflix\iep\iep-spring\5.1.2\iep-spring-5.1.2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\netflix\iep\iep-spring-atlas\5.1.2\iep-spring-atlas-5.1.2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\apache\logging\log4j\log4j-api\2.24.3\log4j-api-2.24.3.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\apache\logging\log4j\log4j-core\2.24.3\log4j-core-2.24.3.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\apache\logging\log4j\log4j-slf4j-impl\2.24.3\log4j-slf4j-impl-2.24.3.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\netflix\spectator\spectator-ext-log4j2\1.8.14\spectator-ext-log4j2-1.8.14.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\springframework\spring-context\6.2.7\spring-context-6.2.7.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-lang\scala-reflect\2.13.16\scala-reflect-2.13.16.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\netflix\iep\iep-service\5.1.2\iep-service-5.1.2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\netflix\spectator\spectator-reg-atlas\1.8.14\spectator-reg-atlas-1.8.14.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\netflix\spectator\spectator-ext-gc\1.8.14\spectator-ext-gc-1.8.14.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\netflix\spectator\spectator-ext-jvm\1.8.14\spectator-ext-jvm-1.8.14.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\netflix\spectator\spectator-nflx-tagging\1.8.14\spectator-nflx-tagging-1.8.14.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\apache\pekko\pekko-actor_2.13\1.1.3\pekko-actor_2.13-1.1.3.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\apache\pekko\pekko-slf4j_2.13\1.1.3\pekko-slf4j_2.13-1.1.3.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\apache\pekko\pekko-stream_2.13\1.1.3\pekko-stream_2.13-1.1.3.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\netflix\iep\iep-dynconfig\5.1.2\iep-dynconfig-5.1.2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\scala-lang\modules\scala-java8-compat_2.13\1.0.2\scala-java8-compat_2.13-1.0.2.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\netflix\spectator\spectator-ext-ipc\1.8.14\spectator-ext-ipc-1.8.14.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\apache\pekko\pekko-http_2.13\1.2.0\pekko-http_2.13-1.2.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\springframework\spring-aop\6.2.7\spring-aop-6.2.7.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\springframework\spring-beans\6.2.7\spring-beans-6.2.7.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\springframework\spring-core\6.2.7\spring-core-6.2.7.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\springframework\spring-expression\6.2.7\spring-expression-6.2.7.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\micrometer\micrometer-observation\1.14.7\micrometer-observation-1.14.7.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\jakarta\annotation\jakarta.annotation-api\3.0.0\jakarta.annotation-api-3.0.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\fasterxml\jackson\core\jackson-core\2.19.0\jackson-core-2.19.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.19.0\jackson-datatype-jdk8-2.19.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.19.0\jackson-datatype-jsr310-2.19.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\fasterxml\jackson\core\jackson-databind\2.19.0\jackson-databind-2.19.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\fasterxml\jackson\module\jackson-module-scala_2.13\2.19.0\jackson-module-scala_2.13-2.19.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\fasterxml\jackson\dataformat\jackson-dataformat-smile\2.19.0\jackson-dataformat-smile-2.19.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\apache\pekko\pekko-protobuf-v3_2.13\1.1.3\pekko-protobuf-v3_2.13-1.1.3.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\typesafe\ssl-config-core_2.13\0.6.1\ssl-config-core_2.13-0.6.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\apache\pekko\pekko-http-core_2.13\1.2.0\pekko-http-core_2.13-1.2.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\github\ben-manes\caffeine\caffeine\3.2.0\caffeine-3.2.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\roaringbitmap\RoaringBitmap\1.3.0\RoaringBitmap-1.3.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\springframework\spring-jcl\6.2.7\spring-jcl-6.2.7.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\io\micrometer\micrometer-commons\1.14.7\micrometer-commons-1.14.7.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\fasterxml\jackson\core\jackson-annotations\2.19.0\jackson-annotations-2.19.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\thoughtworks\paranamer\paranamer\2.8.3\paranamer-2.8.3.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\apache\pekko\pekko-parsing_2.13\1.2.0\pekko-parsing_2.13-1.2.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\parboiled\parboiled_2.13\2.5.1\parboiled_2.13-2.5.1.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\org\jspecify\jspecify\1.0.0\jspecify-1.0.0.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repo1.maven.org\maven2\com\google\errorprone\error_prone_annotations\2.36.0\error_prone_annotations-2.36.0.jar
