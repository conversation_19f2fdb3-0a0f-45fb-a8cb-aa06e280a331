[0m[[0m[0mdebug[0m] [0m[0mPackaging C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-spring-pekko\target\scala-2.13\atlas-spring-pekko_2.13-1.8.0-SNAPSHOT.jar ...[0m
[0m[[0m[0mdebug[0m] [0m[0mInput file mappings:[0m
[0m[[0m[0mdebug[0m] [0m[0m	com[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-spring-pekko\target\scala-2.13\classes\com[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-spring-pekko\target\scala-2.13\classes\com\netflix[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-spring-pekko\target\scala-2.13\classes\com\netflix\atlas[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\pekko[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-spring-pekko\target\scala-2.13\classes\com\netflix\atlas\pekko[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\pekko\ActorSystemService.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-spring-pekko\target\scala-2.13\classes\com\netflix\atlas\pekko\ActorSystemService.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\pekko\MaterializerService.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-spring-pekko\target\scala-2.13\classes\com\netflix\atlas\pekko\MaterializerService.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	com\netflix\atlas\pekko\PekkoConfiguration.class[0m
[0m[[0m[0mdebug[0m] [0m[0m	  C:\Users\<USER>\Documents\augment-projects\security\atlas\atlas-spring-pekko\target\scala-2.13\classes\com\netflix\atlas\pekko\PekkoConfiguration.class[0m
[0m[[0m[0mdebug[0m] [0m[0mDone packaging.[0m
